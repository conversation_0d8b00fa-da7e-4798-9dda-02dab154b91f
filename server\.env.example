# Server Configuration
PORT=8080
ENVIRONMENT=development

# Database Configuration
DATABASE_URL=postgres://username:password@localhost:5432/quester_dev?sslmode=disable
# Alternative SQLite for development:
# DATABASE_URL=sqlite://./quester.db

# Authentication Configuration
JWT_SECRET=your-super-secret-jwt-key-change-this-in-production
JWT_EXPIRATION_HOURS=24
JWT_REFRESH_DAYS=7

# Session Configuration
SESSION_SECRET=your-super-secret-session-key-change-this-in-production

# Redis Configuration (optional - for caching and sessions)
REDIS_URL=redis://localhost:6379
REDIS_PASSWORD=
REDIS_DB=0

# Email Configuration (for notifications)
SMTP_HOST=localhost
SMTP_PORT=587
SMTP_USERNAME=
SMTP_PASSWORD=
FROM_EMAIL=<EMAIL>

# File Upload Configuration
MAX_UPLOAD_SIZE=10485760  # 10MB in bytes
UPLOAD_PATH=./uploads

# API Configuration
API_RATE_LIMIT=100  # requests per minute
ENABLE_RATE_LIMIT=true
ENABLE_COMPRESSION=true

# Security Configuration
ENABLE_HTTPS=false
CERT_FILE=
KEY_FILE=
TRUSTED_PROXIES=

# Logging Configuration
LOG_LEVEL=info
LOG_FORMAT=json

# External Services
OPENAI_API_KEY=

# WebSocket Configuration
WS_MAX_CONNECTIONS=1000
WS_READ_BUFFER_SIZE=1024
WS_WRITE_BUFFER_SIZE=1024
WS_HANDSHAKE_TIMEOUT=10
WS_PING_PERIOD=54
WS_PONG_WAIT=60
WS_WRITE_WAIT=10
WS_MAX_MESSAGE_SIZE=512