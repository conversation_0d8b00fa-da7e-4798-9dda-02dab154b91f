services:
  server:
    build:
      context: ../
      dockerfile: settings/server.dockerfile
      target: production
    deploy:
      replicas: 2
      restart_policy:
        condition: on-failure
        delay: 5s
        max_attempts: 3
        window: 120s
    environment:
      - GO_ENV=production
      - SERVER_PORT=8000
      - DATABASE_URL=${DATABASE_URL}
      - REDIS_URL=${REDIS_URL}
      - JWT_SECRET=${JWT_SECRET}
    depends_on:
      - postgres
      - redis
    networks:
      - quester-production
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 5

  client:
    build:
      context: ../
      dockerfile: settings/client.dockerfile
      target: production-web
    deploy:
      replicas: 2
      restart_policy:
        condition: on-failure
    environment:
      - NODE_ENV=production
      - REACT_APP_API_URL=${API_URL}
    depends_on:
      - server
    networks:
      - quester-production

  postgres:
    image: postgres:15-alpine
    environment:
      - POSTGRES_DB=${POSTGRES_DB}
      - POSTGRES_USER=${POSTGRES_USER}
      - POSTGRES_PASSWORD=${POSTGRES_PASSWORD}
    volumes:
      - postgres_prod_data:/var/lib/postgresql/data
      - ./backups:/backups
      - ./postgres/postgresql.conf:/etc/postgresql/postgresql.conf
    networks:
      - quester-production
    restart: unless-stopped
    command: postgres -c config_file=/etc/postgresql/postgresql.conf

  redis:
    image: redis:7-alpine
    volumes:
      - redis_prod_data:/data
      - ./redis/redis.conf:/etc/redis/redis.conf
    networks:
      - quester-production
    restart: unless-stopped
    command: redis-server /etc/redis/redis.conf

  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/production.conf:/etc/nginx/conf.d/default.conf
      - ./ssl:/etc/nginx/ssl
      - ./logs:/var/log/nginx
    depends_on:
      - server
      - client
    networks:
      - quester-production
    restart: unless-stopped

  backup:
    image: postgres:15-alpine
    environment:
      - POSTGRES_DB=${POSTGRES_DB}
      - POSTGRES_USER=${POSTGRES_USER}
      - POSTGRES_PASSWORD=${POSTGRES_PASSWORD}
    volumes:
      - ./backups:/backups
      - ./scripts/backup.sh:/backup.sh
    networks:
      - quester-production
    entrypoint: /backup.sh
    restart: unless-stopped

volumes:
  postgres_prod_data:
  redis_prod_data:

networks:
  quester-production:
    driver: bridge