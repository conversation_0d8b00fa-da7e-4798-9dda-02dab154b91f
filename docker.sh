#!/bin/bash

# Comprehensive Quester Docker & Environment Management Script
# Merges functionality from: docker.sh, env-switch.sh, env-manager.ps1, hot-reload-restart.sh, test-auth.sh, test-ui-integration.sh
# Usage: ./docker.sh <env> <command> [options]

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
MAGENTA='\033[0;35m'
NC='\033[0m' # No Color

# Get the directory where the script is located
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" &> /dev/null && pwd)"
cd "$SCRIPT_DIR"

ENV=$1
COMMAND=$2
OPTION=$3

# Configuration
SETTINGS_DIR="settings"
TARGET_DIRS=("server" "client")
ENVIRONMENTS=("dev" "staging" "prod")
BASE_URL="http://localhost:8000"
API_URL="$BASE_URL/api/v1"

# Function to print colored output
print_info() {
    echo -e "${CYAN}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_debug() {
    echo -e "${MAGENTA}[DEBUG]${NC} $1"
}

print_header() {
    echo -e "${BLUE}========================================${NC}"
    echo -e "${BLUE}  Quester Environment & Docker Manager${NC}"
    echo -e "${BLUE}========================================${NC}"
}

print_test_header() {
    echo -e "${MAGENTA}========================================${NC}"
    echo -e "${MAGENTA}      Quester Testing Suite${NC}"
    echo -e "${MAGENTA}========================================${NC}"
}

# Function to show usage
show_usage() {
    print_header
    echo ""
    echo "Usage: ./docker.sh <env> <command> [options]"
    echo ""
    echo "Environments:"
    echo "  dev      - Development environment"
    echo "  staging  - Staging environment" 
    echo "  prod     - Production environment"
    echo ""
    echo "Docker Commands:"
    echo "  up       - Switch environment and start all services"
    echo "  down     - Stop all services"
    echo "  restart  - Restart all services"
    echo "  logs     - Show service logs"
    echo "  build    - Rebuild Docker images"
    echo "  server   - Start only server services (database, redis, api)"
    echo "  client   - Start only client service"
    echo "  ps       - Show running containers"
    echo ""
    echo "Environment Commands:"
    echo "  status   - Show environment and container status"
    echo "  switch   - Switch environment only (no Docker commands)"
    echo "  sync     - Sync current environment files"
    echo "  watch    - Watch for environment file changes (experimental)"
    echo ""
    echo "Development Commands:"
    echo "  hot-reload-restart - Restart client container for hot reload"
    echo "  test-auth         - Test authentication system"
    echo "  test-ui           - Test UI integration"
    echo "  test-all          - Run all tests"
    echo ""
    echo "Utility Commands:"
    echo "  aliases           - Show available aliases"
    echo "  health           - Check system health"
    echo "  clean            - Clean up Docker resources"
    echo ""
    echo "Examples:"
    echo "  ./docker.sh dev up                    - Switch to dev and start all services"
    echo "  ./docker.sh prod build                - Switch to prod and rebuild images"
    echo "  ./docker.sh staging logs              - Show logs for staging environment"
    echo "  ./docker.sh dev switch                - Just switch to dev environment"
    echo "  ./docker.sh dev hot-reload-restart    - Restart client for hot reload"
    echo "  ./docker.sh dev test-auth             - Test authentication system"
    echo "  ./docker.sh dev status                - Show comprehensive status"
    echo ""
}

# Function to get current environment
get_current_environment() {
    if [ -f "server/.env" ]; then
        local content=$(cat "server/.env" 2>/dev/null)
        
        if echo "$content" | grep -q "GO_ENV=development\|NODE_ENV=development"; then
            echo "dev"
        elif echo "$content" | grep -q "GO_ENV=staging\|NODE_ENV=staging"; then
            echo "staging"
        elif echo "$content" | grep -q "GO_ENV=production\|NODE_ENV=production"; then
            echo "prod"
        else
            echo "unknown"
        fi
    else
        echo "none"
    fi
}

# Function to switch environment (merged from env-switch.sh)
switch_environment() {
    local env=$1
    local source_file="$SETTINGS_DIR/.env.$env"
    
    if [ ! -f "$source_file" ]; then
        print_error "Source file $source_file not found"
        exit 1
    fi
    
    print_info "Switching to $env environment..."
    
    # Copy to server directory
    if cp "$source_file" "server/.env"; then
        print_success "  server/.env -> $SETTINGS_DIR/.env.$env"
    else
        print_error "Failed to copy environment file to server directory"
        exit 1
    fi
    
    # Copy to client directory
    if cp "$source_file" "client/.env"; then
        print_success "  client/.env -> $SETTINGS_DIR/.env.$env"
    else
        print_error "Failed to copy environment file to client directory"
        exit 1
    fi
    
    print_success "Successfully switched to $env environment"
}

# Function to sync environment files (from env-manager.ps1)
sync_environment_files() {
    local current_env=$(get_current_environment)
    
    if [ "$current_env" = "unknown" ] || [ "$current_env" = "none" ]; then
        print_error "No valid environment detected. Please switch to an environment first."
        exit 1
    fi
    
    print_info "Syncing $current_env environment files..."
    switch_environment "$current_env"
}

# Function to check environment status (enhanced)
check_environment_status() {
    local current_env=$(get_current_environment)
    
    print_info "Current environment: $current_env"
    
    echo ""
    print_info "Environment files status:"
    
    for env in "${ENVIRONMENTS[@]}"; do
        local env_file="$SETTINGS_DIR/.env.$env"
        if [ -f "$env_file" ]; then
            local size=$(du -h "$env_file" 2>/dev/null | cut -f1)
            local modified=$(stat -c %y "$env_file" 2>/dev/null | cut -d' ' -f1)
            echo "  .env.$env -> exists ($size, modified: $modified)"
        else
            echo "  .env.$env -> missing"
        fi
    done
    
    echo ""
    print_info "Target files status:"
    
    for dir in "${TARGET_DIRS[@]}"; do
        local target="$dir/.env"
        if [ -f "$target" ]; then
            local size=$(du -h "$target" 2>/dev/null | cut -f1)
            local modified=$(stat -c %y "$target" 2>/dev/null | cut -d' ' -f1)
            echo "  $target -> exists ($size, modified: $modified)"
            
            # Check if in sync
            if [ -f "$SETTINGS_DIR/.env.$current_env" ]; then
                if cmp -s "$target" "$SETTINGS_DIR/.env.$current_env"; then
                    echo "    ✅ In sync with .env.$current_env"
                else
                    echo "    ⚠️  Out of sync with .env.$current_env"
                fi
            fi
        else
            echo "  $target -> missing"
        fi
    done
    
    echo ""
}

# Function to check Docker status (enhanced)
check_docker_status() {
    print_info "Docker container status:"
    
    if [ -f "$SETTINGS_DIR/docker-compose.$ENV.yml" ]; then
        if command -v docker >/dev/null 2>&1 && command -v docker-compose >/dev/null 2>&1; then
            cd "$SETTINGS_DIR"
            
            echo ""
            docker-compose -f "docker-compose.$ENV.yml" ps --format "table" 2>/dev/null || {
                print_warning "No containers running or Docker not available"
            }
            
            echo ""
            print_info "Docker system status:"
            docker system df 2>/dev/null || true
            
            cd "$SCRIPT_DIR"
        else
            print_warning "Docker or docker-compose not found in PATH"
        fi
    else
        print_warning "Docker compose file for $ENV not found"
    fi
}

# Function for hot reload restart (from hot-reload-restart.sh)
hot_reload_restart() {
    local env=${ENV:-"dev"}
    
    print_info "🔄 Restarting client container for hot reload..."
    
    if ! command -v docker >/dev/null 2>&1; then
        print_error "Docker not found in PATH"
        exit 1
    fi
    
    # Stop the client container
    print_info "📦 Stopping client container..."
    docker stop "quester-client-$env" 2>/dev/null || {
        print_warning "Client container was not running"
    }
    
    # Remove the container to force rebuild with new settings
    print_info "🗑️  Removing old container..."
    docker rm "quester-client-$env" 2>/dev/null || {
        print_warning "Client container did not exist"
    }
    
    # Rebuild and start the client container
    print_info "🔨 Rebuilding client container..."
    cd "$SETTINGS_DIR"
    docker-compose -f "docker-compose.$env.yml" up -d --build client
    cd "$SCRIPT_DIR"
    
    print_success "✅ Client container restarted!"
    print_info "🌐 Metro bundler should be available at http://localhost:8081"
    print_info "📱 Expo DevTools should be available at http://localhost:19000"
    
    # Show logs option
    if [ "$OPTION" = "--logs" ] || [ "$OPTION" = "-l" ]; then
        print_info "📋 Showing client logs (press Ctrl+C to exit)..."
        docker logs -f "quester-client-$env"
    fi
}

# Function to test authentication (from test-auth.sh)
test_auth() {
    print_test_header
    echo
    print_info "🧪 Testing Quester Authentication System"
    echo
    
    # Check if server is running
    print_info "Checking if server is accessible..."
    if ! curl -s "$BASE_URL/health" >/dev/null 2>&1; then
        print_error "Server not accessible at $BASE_URL"
        print_info "Make sure the server is running with: ./docker.sh dev up"
        exit 1
    fi
    
    print_success "Server is accessible"
    echo
    
    # Test 1: Health Check
    print_info "1. Testing Health Check..."
    local health_response=$(curl -s "$BASE_URL/health" 2>/dev/null)
    if [ $? -eq 0 ]; then
        print_success "Health check passed"
        echo "$health_response" | jq '.' 2>/dev/null || echo "$health_response"
    else
        print_error "Health check failed"
    fi
    echo
    
    # Test 2: Register a new user
    print_info "2. Testing User Registration..."
    local timestamp=$(date +%s)
    local register_response=$(curl -s -X POST "$API_URL/auth/register" \
      -H "Content-Type: application/json" \
      -d "{
        \"username\": \"testuser$timestamp\",
        \"email\": \"test$<EMAIL>\",
        \"password\": \"testpassword123\",
        \"first_name\": \"Test\",
        \"last_name\": \"User\"
      }" 2>/dev/null)
    
    if [ $? -eq 0 ]; then
        print_success "Registration request sent"
        echo "$register_response" | jq '.' 2>/dev/null || echo "$register_response"
        
        # Extract access token
        local access_token=$(echo "$register_response" | jq -r '.access_token // .data.access_token // empty' 2>/dev/null)
        if [ -n "$access_token" ] && [ "$access_token" != "null" ]; then
            print_success "Access token obtained"
            
            # Test 3: Access protected profile endpoint
            echo
            print_info "3. Testing Protected Profile Access..."
            local profile_response=$(curl -s -X GET "$API_URL/profile" \
              -H "Authorization: Bearer $access_token" 2>/dev/null)
            
            if [ $? -eq 0 ]; then
                print_success "Profile access successful"
                echo "$profile_response" | jq '.' 2>/dev/null || echo "$profile_response"
            else
                print_error "Profile access failed"
            fi
        else
            print_warning "No access token received in registration response"
        fi
    else
        print_error "Registration failed"
    fi
    
    echo
    print_success "🏁 Authentication system testing completed!"
}

# Function to test UI integration (from test-ui-integration.sh)
test_ui() {
    print_test_header
    echo
    print_info "🧪 Testing Quester UI Integration"
    echo
    
    local errors=0
    
    # Function to check if file exists and has content
    check_file() {
        local file=$1
        local description=$2
        
        if [ -f "$file" ]; then
            local size=$(stat -c%s "$file" 2>/dev/null)
            if [ "$size" -gt 0 ]; then
                print_success "  ✅ $description ($file)"
            else
                print_warning "  ⚠️  $description exists but is empty ($file)"
                ((errors++))
            fi
        else
            print_error "  ❌ $description missing ($file)"
            ((errors++))
        fi
    }
    
    # Function to check directory structure
    check_directory() {
        local dir=$1
        local description=$2
        
        if [ -d "$dir" ]; then
            local count=$(find "$dir" -name "*.tsx" -o -name "*.ts" -o -name "*.js" -o -name "*.jsx" | wc -l)
            print_success "  ✅ $description ($count files)"
        else
            print_error "  ❌ $description directory missing ($dir)"
            ((errors++))
        fi
    }
    
    print_info "1. Checking project structure..."
    echo
    
    # Core directories
    check_directory "client/components/ui" "UI Components"
    check_directory "client/components/auth" "Auth Components"
    check_directory "client/components/layout" "Layout Components"
    check_directory "client/services/api" "API Services"
    check_directory "client/contexts" "React Contexts"
    check_directory "client/hooks" "Custom Hooks"
    check_directory "client/constants" "Design Constants"
    check_directory "client/app" "App Structure"
    
    echo
    print_info "2. Checking core files..."
    echo
    
    # Key files
    check_file "client/package.json" "Package Configuration"
    check_file "client/app.json" "Expo Configuration"
    check_file "client/tsconfig.json" "TypeScript Configuration"
    
    # Design system
    check_file "client/constants/theme.ts" "Theme System"
    check_file "client/hooks/use-theme.ts" "Theme Hook"
    
    # UI Components
    check_file "client/components/ui/button.tsx" "Button Component"
    check_file "client/components/ui/input.tsx" "Input Component"
    check_file "client/components/ui/text.tsx" "Text Component"
    check_file "client/components/ui/card.tsx" "Card Component"
    
    # Auth System
    check_file "client/contexts/auth-context.tsx" "Auth Context"
    check_file "client/components/auth/login-form.tsx" "Login Form"
    check_file "client/components/auth/register-form.tsx" "Register Form"
    
    # API Integration
    check_file "client/services/api/client.ts" "API Client"
    check_file "client/services/api/auth.ts" "Auth Service"
    check_file "client/services/api/types.ts" "API Types"
    
    # App Structure
    check_file "client/app/_layout.tsx" "Root Layout"
    check_file "client/app/(tabs)/_layout.tsx" "Tab Layout"
    check_file "client/app/(tabs)/index.tsx" "Dashboard Screen"
    
    echo
    print_info "3. Checking TypeScript compilation..."
    echo
    
    if [ -d "client" ]; then
        cd client
        
        if command -v npx >/dev/null 2>&1 && [ -f "package.json" ]; then
            if npx tsc --noEmit --skipLibCheck 2>/dev/null; then
                print_success "  ✅ TypeScript compilation successful"
            else
                print_warning "  ⚠️  TypeScript compilation has issues"
                ((errors++))
            fi
        else
            print_warning "  ⚠️  Cannot check TypeScript - npx not found or no package.json"
        fi
        
        cd "$SCRIPT_DIR"
    fi
    
    echo
    print_info "4. Integration Summary"
    echo
    
    if [ $errors -eq 0 ]; then
        print_success "🎉 All UI integration checks passed!"
        echo
        print_info "📱 UI System Status:"
        print_success "  ✅ Component-based architecture implemented"
        print_success "  ✅ TypeScript integration working"
        print_success "  ✅ Authentication system in place"
        print_success "  ✅ API integration configured"
        echo
        print_info "🚀 Ready for Testing!"
        print_info "  1. Start server: ./docker.sh dev up"
        print_info "  2. Start client: cd client && npx expo start"
        print_info "  3. Test authentication flow"
    else
        print_warning "⚠️  Found $errors issues in UI integration"
        print_info "Please address the missing files/directories above"
    fi
}

# Function to run all tests
test_all() {
    print_test_header
    print_info "Running comprehensive test suite..."
    echo
    
    print_info "🔍 Step 1: UI Integration Test"
    echo "================================"
    test_ui
    
    echo
    echo
    print_info "🔒 Step 2: Authentication Test"
    echo "==============================="
    test_auth
    
    echo
    print_success "🏁 All tests completed!"
}

# Function to check system health
check_health() {
    print_info "🏥 System Health Check"
    echo "======================"
    echo
    
    # Check Docker
    if command -v docker >/dev/null 2>&1; then
        if docker info >/dev/null 2>&1; then
            print_success "✅ Docker is running"
        else
            print_error "❌ Docker is installed but not running"
        fi
    else
        print_error "❌ Docker is not installed"
    fi
    
    # Check Docker Compose
    if command -v docker-compose >/dev/null 2>&1; then
        print_success "✅ Docker Compose is available"
    else
        print_error "❌ Docker Compose is not installed"
    fi
    
    # Check Node.js
    if command -v node >/dev/null 2>&1; then
        local node_version=$(node --version)
        print_success "✅ Node.js is available ($node_version)"
    else
        print_warning "⚠️  Node.js is not in PATH"
    fi
    
    # Check essential files
    if [ -f "$SETTINGS_DIR/deploy.sh" ]; then
        print_success "✅ Deploy script is available"
    else
        print_error "❌ Deploy script is missing"
    fi
    
    # Check environment files
    local env_files_ok=true
    for env in "${ENVIRONMENTS[@]}"; do
        if [ ! -f "$SETTINGS_DIR/.env.$env" ]; then
            print_error "❌ Environment file .env.$env is missing"
            env_files_ok=false
        fi
    done
    
    if [ "$env_files_ok" = true ]; then
        print_success "✅ All environment files are present"
    fi
    
    echo
    
    # Check current environment
    local current_env=$(get_current_environment)
    if [ "$current_env" != "none" ] && [ "$current_env" != "unknown" ]; then
        print_success "✅ Current environment: $current_env"
    else
        print_warning "⚠️  No valid environment detected"
    fi
}

# Function to show aliases (merged from docker-aliases.sh and env-aliases.sh)
show_aliases() {
    print_info "Available Docker & Environment Aliases"
    echo "======================================"
    echo
    echo "Add these to your shell profile (.bashrc, .zshrc, etc.):"
    echo
    echo "# Environment switching aliases (from env-aliases.sh)"
    echo "alias env-dev='./docker.sh dev switch'"
    echo "alias env-staging='./docker.sh staging switch'"  
    echo "alias env-prod='./docker.sh prod switch'"
    echo "alias env-status='./docker.sh dev status'"
    echo "alias env-sync='./docker.sh dev sync'"
    echo "alias env-watch='./docker.sh dev watch'"
    echo "alias envs='./docker.sh dev status'"
    echo
    echo "# Quick Docker operations (from docker-aliases.sh)"
    echo "alias docker-dev='./docker.sh dev'"
    echo "alias docker-staging='./docker.sh staging'"
    echo "alias docker-prod='./docker.sh prod'"
    echo "alias docker-dev-up='./docker.sh dev up'"
    echo "alias docker-dev-down='./docker.sh dev down'"
    echo "alias docker-dev-logs='./docker.sh dev logs'"
    echo "alias docker-dev-restart='./docker.sh dev restart'"
    echo
    echo "alias docker-staging-up='./docker.sh staging up'"
    echo "alias docker-staging-down='./docker.sh staging down'"
    echo "alias docker-staging-logs='./docker.sh staging logs'"
    echo
    echo "alias docker-prod-up='./docker.sh prod up'"
    echo "alias docker-prod-down='./docker.sh prod down'"
    echo "alias docker-prod-logs='./docker.sh prod logs'"
    echo
    echo "# Development shortcuts"
    echo "alias dev-server='./docker.sh dev server'"
    echo "alias dev-client='./docker.sh dev client'"
    echo "alias dev-restart='./docker.sh dev restart'"
    echo "alias dev-hot-reload='./docker.sh dev hot-reload-restart'"
    echo
    echo "# Status and testing"
    echo "alias docker-status='./docker.sh dev status'"
    echo "alias quester-status='./docker.sh dev status && echo \"\" && docker ps --format \"table {{.Names}}\\t{{.Image}}\\t{{.Status}}\"'"
    echo "alias test-auth='./docker.sh dev test-auth'"
    echo "alias test-ui='./docker.sh dev test-ui'"
    echo "alias test-all='./docker.sh dev test-all'"
    echo
    echo "# Quick environment checks"
    echo "alias env-current='grep \"GO_ENV\\|NODE_ENV\" server/.env 2>/dev/null || echo \"No environment file found\"'"
    echo "alias docker-ps='docker ps --format \"table {{.Names}}\\t{{.Image}}\\t{{.Status}}\\t{{.Ports}}\"'"
    echo
    echo "Available commands summary:"
    echo "  env-dev, env-staging, env-prod  - Quick environment switching"
    echo "  docker-dev-up, docker-staging-up, docker-prod-up  - Start environments"
    echo "  dev-server, dev-client  - Start individual services"
    echo "  quester-status  - Comprehensive status check"
    echo "  test-auth, test-ui, test-all  - Testing shortcuts"
}

# Function to clean Docker resources
clean_docker() {
    print_info "🧹 Cleaning Docker resources..."
    
    if ! command -v docker >/dev/null 2>&1; then
        print_error "Docker not found in PATH"
        exit 1
    fi
    
    # Remove stopped containers
    print_info "Removing stopped containers..."
    docker container prune -f
    
    # Remove unused images
    print_info "Removing unused images..."
    docker image prune -f
    
    # Remove unused volumes
    print_info "Removing unused volumes..."
    docker volume prune -f
    
    # Remove unused networks
    print_info "Removing unused networks..."
    docker network prune -f
    
    print_success "Docker cleanup completed!"
    
    # Show disk usage
    echo
    print_info "Docker disk usage after cleanup:"
    docker system df
}

# Validate inputs
if [ -z "$ENV" ] || [ -z "$COMMAND" ]; then
    show_usage
    exit 1
fi

# Validate environment
if [[ ! "$ENV" =~ ^(dev|staging|prod)$ ]]; then
    print_error "Environment must be dev, staging, or prod"
    show_usage
    exit 1
fi

# Validate command
valid_commands="up down restart logs build server client ps status switch sync watch hot-reload-restart test-auth test-ui test-all aliases health clean"
if [[ ! " $valid_commands " =~ " $COMMAND " ]]; then
    print_error "Unknown command '$COMMAND'"
    show_usage
    exit 1
fi

# Print header
print_header
echo ""

# Handle special commands that don't require Docker
case $COMMAND in
    status)
        check_environment_status
        check_docker_status
        exit 0
        ;;
    switch)
        switch_environment "$ENV"
        print_success "Environment switched to $ENV (Docker services not affected)"
        exit 0
        ;;
    sync)
        sync_environment_files
        exit 0
        ;;
    watch)
        print_info "Starting environment file watcher (bash implementation)..."
        print_info "Watching for changes in: $SETTINGS_DIR/.env.{dev,staging,prod}"
        print_info "Press Ctrl+C to stop watching"
        echo ""
        
        # Simple polling-based watch implementation
        current_env=$(get_current_environment)
        last_mod_time=""
        source_file="$SETTINGS_DIR/.env.$current_env"
        
        if [ ! -f "$source_file" ]; then
            print_error "Source file $source_file not found"
            exit 1
        fi
        
        while true; do
            # Check if the current environment file has been modified
            if [ -f "$source_file" ]; then
                mod_time=$(stat -c %Y "$source_file" 2>/dev/null || stat -f %m "$source_file" 2>/dev/null)
                if [ "$mod_time" != "$last_mod_time" ] && [ -n "$last_mod_time" ]; then
                    print_info "Detected change in $source_file"
                    print_info "Auto-syncing environment files..."
                    sync_environment_files
                    print_success "Files synced successfully"
                    echo ""
                fi
                last_mod_time="$mod_time"
            fi
            sleep 2
        done
        ;;
    hot-reload-restart)
        switch_environment "$ENV"
        hot_reload_restart
        exit 0
        ;;
    test-auth)
        switch_environment "$ENV"
        test_auth
        exit 0
        ;;
    test-ui)
        test_ui
        exit 0
        ;;
    test-all)
        switch_environment "$ENV"
        test_all
        exit 0
        ;;
    aliases)
        show_aliases
        exit 0
        ;;
    health)
        check_health
        exit 0
        ;;
    clean)
        clean_docker
        exit 0
        ;;
esac

# For Docker commands, switch environment first
print_info "Processing command '$COMMAND' for environment '$ENV'"
echo ""

# Switch environment
switch_environment "$ENV"

# Check if deploy.sh exists
if [ ! -f "$SETTINGS_DIR/deploy.sh" ]; then
    print_error "$SETTINGS_DIR/deploy.sh not found"
    exit 1
fi

# Make deploy.sh executable if it isn't
if [ ! -x "$SETTINGS_DIR/deploy.sh" ]; then
    print_info "Making deploy.sh executable..."
    chmod +x "$SETTINGS_DIR/deploy.sh"
fi

# Run the deploy command
print_info "Executing Docker command..."
echo ""

cd "$SETTINGS_DIR"
./deploy.sh "$ENV" "$COMMAND"
deploy_exit_code=$?

# Return to original directory
cd "$SCRIPT_DIR"

# Check if deployment was successful
if [ $deploy_exit_code -eq 0 ]; then
    echo ""
    print_success "Command '$COMMAND' completed successfully for environment '$ENV'"
    
    # Show quick status for certain commands
    case $COMMAND in
        up|restart|server|client)
            echo ""
            print_info "Quick status check:"
            cd "$SETTINGS_DIR"
            docker-compose -f "docker-compose.$ENV.yml" ps --format "table" 2>/dev/null || true
            cd "$SCRIPT_DIR"
            
            # Show useful URLs for development
            if [ "$ENV" = "dev" ]; then
                echo ""
                print_info "Development URLs:"
                print_info "  🌐 API Server: http://localhost:8000"
                print_info "  📱 Metro Bundler: http://localhost:8081"
                print_info "  🔧 Expo DevTools: http://localhost:19000"
                print_info "  💾 Database: localhost:5432"
                print_info "  🗄️  Redis: localhost:6379"
                print_info "  📧 MailHog: http://localhost:8025"
            fi
            ;;
    esac
else
    echo ""
    print_error "Command '$COMMAND' failed for environment '$ENV'"
    exit $deploy_exit_code
fi
