package handlers

import (
    "github.com/gofiber/fiber/v2"
    "gorm.io/gorm"
    "quester/internal/models"
)

type {{HandlerName}}Handler struct {
    db *gorm.DB
}

func New{{HandlerName}}Handler(db *gorm.DB) *{{HandlerName}}Handler {
    return &{{HandlerName}}Handler{db: db}
}

func (h *{{HandlerName}}Handler) Get{{HandlerName}}s(c *fiber.Ctx) error {
    var {{handlerName}}s []models.{{HandlerName}}

    if err := h.db.Find(&{{handlerName}}s).Error; err != nil {
        return c.Status(500).JSON(fiber.Map{
            "error": "Could not fetch {{handlerName}}s",
        })
    }

    return c.JSON(fiber.Map{
        "data": {{handlerName}}s,
    })
}

func (h *{{HandlerName}}Handler) Get{{HandlerName}}(c *fiber.Ctx) error {
    id := c.Para<PERSON>("id")

    var {{handlerName}} models.{{HandlerName}}
    if err := h.db.First(&{{handlerName}}, id).Error; err != nil {
        if errors.Is(err, gorm.ErrRecordNotFound) {
            return c.Status(404).JSON(fiber.Map{
                "error": "{{HandlerName}} not found",
            })
        }
        return c.Status(500).JSON(fiber.Map{
            "error": "Could not fetch {{handlerName}}",
        })
    }

    return c.JSON(fiber.Map{
        "data": {{handlerName}},
    })
}

func (h *{{HandlerName}}Handler) Create{{HandlerName}}(c *fiber.Ctx) error {
    var req Create{{HandlerName}}Request
    if err := c.BodyParser(&req); err != nil {
        return c.Status(400).JSON(fiber.Map{
            "error": "Invalid request body",
        })
    }

    {{handlerName}} := models.{{HandlerName}}{
        // Map request fields to model
    }

    if err := h.db.Create(&{{handlerName}}).Error; err != nil {
        return c.Status(500).JSON(fiber.Map{
            "error": "Could not create {{handlerName}}",
        })
    }

    return c.Status(201).JSON(fiber.Map{
        "data": {{handlerName}},
    })
}

func (h *{{HandlerName}}Handler) Update{{HandlerName}}(c *fiber.Ctx) error {
    id := c.Params("id")

    var {{handlerName}} models.{{HandlerName}}
    if err := h.db.First(&{{handlerName}}, id).Error; err != nil {
        if errors.Is(err, gorm.ErrRecordNotFound) {
            return c.Status(404).JSON(fiber.Map{
                "error": "{{HandlerName}} not found",
            })
        }
        return c.Status(500).JSON(fiber.Map{
            "error": "Could not fetch {{handlerName}}",
        })
    }

    var req Update{{HandlerName}}Request
    if err := c.BodyParser(&req); err != nil {
        return c.Status(400).JSON(fiber.Map{
            "error": "Invalid request body",
        })
    }

    // Update fields from request

    if err := h.db.Save(&{{handlerName}}).Error; err != nil {
        return c.Status(500).JSON(fiber.Map{
            "error": "Could not update {{handlerName}}",
        })
    }

    return c.JSON(fiber.Map{
        "data": {{handlerName}},
    })
}

func (h *{{HandlerName}}Handler) Delete{{HandlerName}}(c *fiber.Ctx) error {
    id := c.Params("id")

    var {{handlerName}} models.{{HandlerName}}
    if err := h.db.First(&{{handlerName}}, id).Error; err != nil {
        if errors.Is(err, gorm.ErrRecordNotFound) {
            return c.Status(404).JSON(fiber.Map{
                "error": "{{HandlerName}} not found",
            })
        }
        return c.Status(500).JSON(fiber.Map{
            "error": "Could not fetch {{handlerName}}",
        })
    }

    if err := h.db.Delete(&{{handlerName}}).Error; err != nil {
        return c.Status(500).JSON(fiber.Map{
            "error": "Could not delete {{handlerName}}",
        })
    }

    return c.JSON(fiber.Map{
        "message": "{{HandlerName}} deleted successfully",
    })
}