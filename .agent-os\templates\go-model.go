package models

import (
    "time"
    "gorm.io/gorm"
)

type {{ModelName}} struct {
    ID        uint           `gorm:"primarykey" json:"id"`
    CreatedAt time.Time      `json:"created_at"`
    UpdatedAt time.Time      `json:"updated_at"`
    DeletedAt gorm.DeletedAt `gorm:"index" json:"deleted_at"`

    // Add your model fields here
    // Example:
    // Name        string `gorm:"not null" json:"name"`
    // Description string `json:"description"`
    // Status      string `gorm:"default:'active'" json:"status"`

    // Foreign keys and relationships
    // UserID      uint `gorm:"not null" json:"user_id"`
    // User        User `gorm:"foreignKey:UserID" json:"user"`

    // Many-to-many relationships
    // Tags        []Tag `gorm:"many2many:{{modelName}}_tags" json:"tags"`

    // Has many relationships
    // Items       []Item `gorm:"foreignKey:{{ModelName}}ID" json:"items"`
}

// Table name override (optional)
func ({{ModelName}}) TableName() string {
    return "{{tableName}}"
}