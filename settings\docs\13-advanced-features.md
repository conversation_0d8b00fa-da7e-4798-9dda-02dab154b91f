# Advanced Features Implementation

## Overview

Advanced features implementation guide for the Quester application including gamification, notifications, file handling, search functionality, and analytics.

## Gamification System

### Achievement Engine (server/internal/services/achievement.go)

```go
package services

import (
    "fmt"
    "time"
    "gorm.io/gorm"
    "quester-server/internal/models"
)

type AchievementService struct {
    db *gorm.DB
}

func NewAchievementService(db *gorm.DB) *AchievementService {
    return &AchievementService{db: db}
}

// Achievement types and their unlock conditions
var AchievementDefinitions = map[string]models.AchievementDefinition{
    "first_quest": {
        Name:        "Quest Starter",
        Description: "Complete your first quest",
        BadgeIcon:   "trophy",
        BadgeColor:  "#FFD700",
        Points:      50,
        Category:    "milestone",
        Rarity:      "common",
        UnlockConditions: map[string]interface{}{
            "quests_completed": 1,
        },
    },
    "speed_demon": {
        Name:        "Speed Demon",
        Description: "Complete 5 quests in one day",
        BadgeIcon:   "flash",
        BadgeColor:  "#FF4500",
        Points:      100,
        Category:    "streak",
        Rarity:      "rare",
        UnlockConditions: map[string]interface{}{
            "quests_completed_today": 5,
        },
    },
    "team_player": {
        Name:        "Team Player",
        Description: "Join 10 collaborative quests",
        BadgeIcon:   "people",
        BadgeColor:  "#4169E1",
        Points:      75,
        Category:    "social",
        Rarity:      "uncommon",
        UnlockConditions: map[string]interface{}{
            "collaborative_quests_joined": 10,
        },
    },
    "knowledge_seeker": {
        Name:        "Knowledge Seeker",
        Description: "Complete 50 learning quests",
        BadgeIcon:   "book",
        BadgeColor:  "#32CD32",
        Points:      200,
        Category:    "learning",
        Rarity:      "epic",
        UnlockConditions: map[string]interface{}{
            "learning_quests_completed": 50,
        },
    },
    "legend": {
        Name:        "Legend",
        Description: "Reach 10,000 total points",
        BadgeIcon:   "crown",
        BadgeColor:  "#8A2BE2",
        Points:      500,
        Category:    "milestone",
        Rarity:      "legendary",
        UnlockConditions: map[string]interface{}{
            "total_points": 10000,
        },
    },
}

func (s *AchievementService) CheckAchievements(userID uint, eventType string, eventData map[string]interface{}) error {
    user, err := s.getUserWithStats(userID)
    if err != nil {
        return err
    }

    // Get current user achievements
    var currentAchievements []models.Achievement
    err = s.db.Where("user_id = ?", userID).Find(&currentAchievements).Error
    if err != nil {
        return err
    }

    achievementMap := make(map[string]bool)
    for _, ach := range currentAchievements {
        achievementMap[ach.Name] = ach.IsCompleted
    }

    // Check each achievement definition
    for key, definition := range AchievementDefinitions {
        // Skip if already completed
        if achievementMap[definition.Name] {
            continue
        }

        if s.checkUnlockConditions(user, definition.UnlockConditions, eventData) {
            err = s.unlockAchievement(userID, definition)
            if err != nil {
                return err
            }
        }
    }

    return nil
}

func (s *AchievementService) checkUnlockConditions(user *models.UserWithStats, conditions map[string]interface{}, eventData map[string]interface{}) bool {
    for condition, requiredValue := range conditions {
        switch condition {
        case "quests_completed":
            if user.QuestsCompleted < requiredValue.(int) {
                return false
            }
        case "quests_completed_today":
            if user.QuestsCompletedToday < requiredValue.(int) {
                return false
            }
        case "collaborative_quests_joined":
            if user.CollaborativeQuestsJoined < requiredValue.(int) {
                return false
            }
        case "learning_quests_completed":
            if user.LearningQuestsCompleted < requiredValue.(int) {
                return false
            }
        case "total_points":
            if user.TotalPoints < requiredValue.(int) {
                return false
            }
        case "consecutive_days":
            if user.ConsecutiveDays < requiredValue.(int) {
                return false
            }
        }
    }
    return true
}

func (s *AchievementService) unlockAchievement(userID uint, definition models.AchievementDefinition) error {
    achievement := models.Achievement{
        UserID:      userID,
        Name:        definition.Name,
        Description: definition.Description,
        BadgeIcon:   definition.BadgeIcon,
        BadgeColor:  definition.BadgeColor,
        Points:      definition.Points,
        Category:    definition.Category,
        Rarity:      definition.Rarity,
        IsCompleted: true,
        UnlockedAt:  time.Now(),
    }

    err := s.db.Create(&achievement).Error
    if err != nil {
        return err
    }

    // Award points to user
    err = s.awardPoints(userID, definition.Points)
    if err != nil {
        return err
    }

    // Send notification
    err = s.sendAchievementNotification(userID, achievement)
    if err != nil {
        return err
    }

    return nil
}

func (s *AchievementService) awardPoints(userID uint, points int) error {
    return s.db.Model(&models.User{}).
        Where("id = ?", userID).
        Update("total_points", gorm.Expr("total_points + ?", points)).Error
}

func (s *AchievementService) GetLeaderboard(period string, limit int) ([]models.LeaderboardEntry, error) {
    var entries []models.LeaderboardEntry

    query := s.db.Table("users").
        Select("users.id, users.username, users.avatar, users.total_points as score, users.level").
        Where("users.is_active = ?", true).
        Order("users.total_points DESC").
        Limit(limit)

    // Add period-specific filtering if needed
    switch period {
    case "weekly":
        query = query.Where("DATE(users.updated_at) >= DATE('now', '-7 days')")
    case "monthly":
        query = query.Where("DATE(users.updated_at) >= DATE('now', '-30 days')")
    }

    err := query.Find(&entries).Error
    if err != nil {
        return nil, err
    }

    // Add rank numbers
    for i := range entries {
        entries[i].Rank = i + 1
    }

    return entries, nil
}
```

### Points and Leveling System (server/internal/services/leveling.go)

```go
package services

import (
    "math"
    "gorm.io/gorm"
    "quester-server/internal/models"
)

type LevelingService struct {
    db *gorm.DB
}

func NewLevelingService(db *gorm.DB) *LevelingService {
    return &LevelingService{db: db}
}

// Calculate required points for level (exponential growth)
func (s *LevelingService) GetRequiredPointsForLevel(level int) int {
    if level <= 1 {
        return 0
    }
    // Formula: 100 * (level^1.5)
    return int(100 * math.Pow(float64(level), 1.5))
}

func (s *LevelingService) GetLevelFromPoints(points int) int {
    level := 1
    for {
        required := s.GetRequiredPointsForLevel(level + 1)
        if points < required {
            break
        }
        level++
    }
    return level
}

func (s *LevelingService) UpdateUserLevel(userID uint) error {
    var user models.User
    err := s.db.First(&user, userID).Error
    if err != nil {
        return err
    }

    newLevel := s.GetLevelFromPoints(user.TotalPoints)

    if newLevel > user.Level {
        // Level up!
        oldLevel := user.Level
        user.Level = newLevel

        err = s.db.Save(&user).Error
        if err != nil {
            return err
        }

        // Send level up notification
        err = s.sendLevelUpNotification(userID, oldLevel, newLevel)
        if err != nil {
            return err
        }

        // Check for level-based achievements
        err = s.checkLevelAchievements(userID, newLevel)
        if err != nil {
            return err
        }
    }

    return nil
}

func (s *LevelingService) AwardQuestCompletionPoints(questID uint, userID uint) error {
    var quest models.Quest
    err := s.db.First(&quest, questID).Error
    if err != nil {
        return err
    }

    // Base points from quest
    basePoints := quest.Points

    // Bonus points based on difficulty
    difficultyMultiplier := map[string]float64{
        "beginner":     1.0,
        "intermediate": 1.2,
        "advanced":     1.5,
        "expert":       2.0,
    }

    multiplier := difficultyMultiplier[quest.Difficulty]
    if multiplier == 0 {
        multiplier = 1.0
    }

    totalPoints := int(float64(basePoints) * multiplier)

    // Award points
    err = s.db.Model(&models.User{}).
        Where("id = ?", userID).
        Update("total_points", gorm.Expr("total_points + ?", totalPoints)).Error
    if err != nil {
        return err
    }

    // Update level
    return s.UpdateUserLevel(userID)
}
```

## Advanced Search and Filtering

### Search Service (server/internal/services/search.go)

```go
package services

import (
    "strings"
    "gorm.io/gorm"
    "quester-server/internal/models"
)

type SearchService struct {
    db *gorm.DB
}

type SearchResult struct {
    Type        string      `json:"type"`
    ID          uint        `json:"id"`
    Title       string      `json:"title"`
    Description string      `json:"description"`
    Score       float64     `json:"score"`
    Highlights  []string    `json:"highlights"`
    Data        interface{} `json:"data"`
}

type SearchFilters struct {
    Query      string   `json:"query"`
    Types      []string `json:"types"`
    Categories []string `json:"categories"`
    Tags       []string `json:"tags"`
    DateFrom   *time.Time `json:"date_from"`
    DateTo     *time.Time `json:"date_to"`
    UserID     *uint    `json:"user_id"`
    Status     []string `json:"status"`
    Priority   []string `json:"priority"`
    Limit      int      `json:"limit"`
    Offset     int      `json:"offset"`
}

func NewSearchService(db *gorm.DB) *SearchService {
    return &SearchService{db: db}
}

func (s *SearchService) Search(filters SearchFilters) ([]SearchResult, int64, error) {
    var results []SearchResult
    var total int64

    // Search quests
    if len(filters.Types) == 0 || contains(filters.Types, "quest") {
        questResults, err := s.searchQuests(filters)
        if err != nil {
            return nil, 0, err
        }
        results = append(results, questResults...)
    }

    // Search tasks
    if len(filters.Types) == 0 || contains(filters.Types, "task") {
        taskResults, err := s.searchTasks(filters)
        if err != nil {
            return nil, 0, err
        }
        results = append(results, taskResults...)
    }

    // Search users
    if len(filters.Types) == 0 || contains(filters.Types, "user") {
        userResults, err := s.searchUsers(filters)
        if err != nil {
            return nil, 0, err
        }
        results = append(results, userResults...)
    }

    // Sort by relevance score
    sort.Slice(results, func(i, j int) bool {
        return results[i].Score > results[j].Score
    })

    // Apply pagination
    total = int64(len(results))
    start := filters.Offset
    end := start + filters.Limit

    if start > len(results) {
        return []SearchResult{}, total, nil
    }
    if end > len(results) {
        end = len(results)
    }

    return results[start:end], total, nil
}

func (s *SearchService) searchQuests(filters SearchFilters) ([]SearchResult, error) {
    query := s.db.Model(&models.Quest{}).
        Preload("CreatedBy").
        Preload("Category")

    // Apply text search
    if filters.Query != "" {
        searchTerm := "%" + strings.ToLower(filters.Query) + "%"
        query = query.Where(
            "LOWER(title) LIKE ? OR LOWER(description) LIKE ? OR LOWER(array_to_string(tags, ' ')) LIKE ?",
            searchTerm, searchTerm, searchTerm,
        )
    }

    // Apply filters
    if len(filters.Status) > 0 {
        query = query.Where("status IN ?", filters.Status)
    }
    if len(filters.Priority) > 0 {
        query = query.Where("priority IN ?", filters.Priority)
    }
    if len(filters.Categories) > 0 {
        query = query.Joins("JOIN categories ON quests.category_id = categories.id").
            Where("categories.name IN ?", filters.Categories)
    }
    if filters.UserID != nil {
        query = query.Where("created_by_id = ? OR id IN (SELECT quest_id FROM quest_participants WHERE user_id = ?)",
            *filters.UserID, *filters.UserID)
    }
    if filters.DateFrom != nil {
        query = query.Where("created_at >= ?", *filters.DateFrom)
    }
    if filters.DateTo != nil {
        query = query.Where("created_at <= ?", *filters.DateTo)
    }

    var quests []models.Quest
    err := query.Find(&quests).Error
    if err != nil {
        return nil, err
    }

    var results []SearchResult
    for _, quest := range quests {
        score := s.calculateQuestRelevanceScore(quest, filters.Query)
        highlights := s.generateHighlights(quest.Title+" "+quest.Description, filters.Query)

        results = append(results, SearchResult{
            Type:        "quest",
            ID:          quest.ID,
            Title:       quest.Title,
            Description: quest.Description,
            Score:       score,
            Highlights:  highlights,
            Data:        quest,
        })
    }

    return results, nil
}

func (s *SearchService) calculateQuestRelevanceScore(quest models.Quest, query string) float64 {
    if query == "" {
        return 1.0
    }

    score := 0.0
    queryLower := strings.ToLower(query)

    // Title matches have higher weight
    if strings.Contains(strings.ToLower(quest.Title), queryLower) {
        score += 2.0
    }

    // Description matches
    if strings.Contains(strings.ToLower(quest.Description), queryLower) {
        score += 1.0
    }

    // Tag matches
    for _, tag := range quest.Tags {
        if strings.Contains(strings.ToLower(tag), queryLower) {
            score += 1.5
        }
    }

    // Priority boost
    priorityBoost := map[string]float64{
        "critical": 0.3,
        "high":     0.2,
        "medium":   0.1,
        "low":      0.0,
    }
    score += priorityBoost[quest.Priority]

    // Recency boost (newer quests get slight boost)
    daysSinceCreation := time.Since(quest.CreatedAt).Hours() / 24
    if daysSinceCreation < 7 {
        score += 0.1
    }

    return score
}

func (s *SearchService) generateHighlights(text, query string) []string {
    if query == "" {
        return []string{}
    }

    words := strings.Fields(strings.ToLower(query))
    textLower := strings.ToLower(text)

    var highlights []string
    for _, word := range words {
        if strings.Contains(textLower, word) {
            // Find context around the match
            index := strings.Index(textLower, word)
            start := max(0, index-30)
            end := min(len(text), index+len(word)+30)

            highlight := text[start:end]
            if start > 0 {
                highlight = "..." + highlight
            }
            if end < len(text) {
                highlight = highlight + "..."
            }

            highlights = append(highlights, highlight)
        }
    }

    return highlights
}
```

### Advanced Filtering (client/hooks/useAdvancedFilters.ts)

```typescript
import { useState, useCallback, useMemo } from 'react';

interface FilterOptions {
  search: string;
  types: string[];
  status: string[];
  priority: string[];
  categories: string[];
  tags: string[];
  dateRange: {
    from: Date | null;
    to: Date | null;
  };
  sortBy: string;
  sortOrder: 'asc' | 'desc';
}

interface UseAdvancedFiltersProps {
  onFiltersChange?: (filters: FilterOptions) => void;
  defaultFilters?: Partial<FilterOptions>;
}

export const useAdvancedFilters = ({
  onFiltersChange,
  defaultFilters = {},
}: UseAdvancedFiltersProps = {}) => {
  const [filters, setFilters] = useState<FilterOptions>({
    search: '',
    types: [],
    status: [],
    priority: [],
    categories: [],
    tags: [],
    dateRange: {
      from: null,
      to: null,
    },
    sortBy: 'updated_at',
    sortOrder: 'desc',
    ...defaultFilters,
  });

  const updateFilter = useCallback((key: keyof FilterOptions, value: any) => {
    setFilters(prev => {
      const newFilters = { ...prev, [key]: value };
      onFiltersChange?.(newFilters);
      return newFilters;
    });
  }, [onFiltersChange]);

  const updateSearch = useCallback((search: string) => {
    updateFilter('search', search);
  }, [updateFilter]);

  const toggleFilter = useCallback((key: keyof Pick<FilterOptions, 'types' | 'status' | 'priority' | 'categories' | 'tags'>, value: string) => {
    setFilters(prev => {
      const currentArray = prev[key] as string[];
      const newArray = currentArray.includes(value)
        ? currentArray.filter(item => item !== value)
        : [...currentArray, value];

      const newFilters = { ...prev, [key]: newArray };
      onFiltersChange?.(newFilters);
      return newFilters;
    });
  }, [onFiltersChange]);

  const setDateRange = useCallback((from: Date | null, to: Date | null) => {
    updateFilter('dateRange', { from, to });
  }, [updateFilter]);

  const setSorting = useCallback((sortBy: string, sortOrder: 'asc' | 'desc') => {
    setFilters(prev => {
      const newFilters = { ...prev, sortBy, sortOrder };
      onFiltersChange?.(newFilters);
      return newFilters;
    });
  }, [onFiltersChange]);

  const clearFilters = useCallback(() => {
    const clearedFilters: FilterOptions = {
      search: '',
      types: [],
      status: [],
      priority: [],
      categories: [],
      tags: [],
      dateRange: { from: null, to: null },
      sortBy: 'updated_at',
      sortOrder: 'desc',
    };
    setFilters(clearedFilters);
    onFiltersChange?.(clearedFilters);
  }, [onFiltersChange]);

  const hasActiveFilters = useMemo(() => {
    return (
      filters.search.length > 0 ||
      filters.types.length > 0 ||
      filters.status.length > 0 ||
      filters.priority.length > 0 ||
      filters.categories.length > 0 ||
      filters.tags.length > 0 ||
      filters.dateRange.from !== null ||
      filters.dateRange.to !== null
    );
  }, [filters]);

  const activeFilterCount = useMemo(() => {
    let count = 0;
    if (filters.search.length > 0) count++;
    if (filters.types.length > 0) count++;
    if (filters.status.length > 0) count++;
    if (filters.priority.length > 0) count++;
    if (filters.categories.length > 0) count++;
    if (filters.tags.length > 0) count++;
    if (filters.dateRange.from || filters.dateRange.to) count++;
    return count;
  }, [filters]);

  return {
    filters,
    updateFilter,
    updateSearch,
    toggleFilter,
    setDateRange,
    setSorting,
    clearFilters,
    hasActiveFilters,
    activeFilterCount,
  };
};
```

## File Upload and Management

### File Upload Service (server/internal/services/upload.go)

```go
package services

import (
    "fmt"
    "io"
    "mime/multipart"
    "os"
    "path/filepath"
    "strings"
    "time"
    "crypto/md5"
    "encoding/hex"

    "github.com/gin-gonic/gin"
    "gorm.io/gorm"
    "quester-server/internal/models"
)

type UploadService struct {
    db         *gorm.DB
    uploadPath string
    maxSize    int64
}

type UploadResult struct {
    ID       uint   `json:"id"`
    Filename string `json:"filename"`
    URL      string `json:"url"`
    Size     int64  `json:"size"`
    MimeType string `json:"mime_type"`
    Hash     string `json:"hash"`
}

func NewUploadService(db *gorm.DB, uploadPath string, maxSize int64) *UploadService {
    return &UploadService{
        db:         db,
        uploadPath: uploadPath,
        maxSize:    maxSize,
    }
}

func (s *UploadService) UploadFile(c *gin.Context, file *multipart.FileHeader, userID uint, entityType string, entityID *uint) (*UploadResult, error) {
    // Validate file size
    if file.Size > s.maxSize {
        return nil, fmt.Errorf("file size exceeds maximum allowed size of %d bytes", s.maxSize)
    }

    // Validate file type
    if !s.isAllowedFileType(file.Header.Get("Content-Type")) {
        return nil, fmt.Errorf("file type not allowed")
    }

    // Open uploaded file
    src, err := file.Open()
    if err != nil {
        return nil, err
    }
    defer src.Close()

    // Generate unique filename
    uniqueFilename := s.generateUniqueFilename(file.Filename)

    // Create upload directory if it doesn't exist
    uploadDir := filepath.Join(s.uploadPath, entityType)
    err = os.MkdirAll(uploadDir, 0755)
    if err != nil {
        return nil, err
    }

    // Create destination file
    dst, err := os.Create(filepath.Join(uploadDir, uniqueFilename))
    if err != nil {
        return nil, err
    }
    defer dst.Close()

    // Calculate hash while copying
    hash := md5.New()
    writer := io.MultiWriter(dst, hash)

    // Copy file content
    written, err := io.Copy(writer, src)
    if err != nil {
        return nil, err
    }

    fileHash := hex.EncodeToString(hash.Sum(nil))

    // Save file record to database
    attachment := models.Attachment{
        UserID:       userID,
        Filename:     file.Filename,
        StoredName:   uniqueFilename,
        MimeType:     file.Header.Get("Content-Type"),
        Size:         written,
        Hash:         fileHash,
        EntityType:   entityType,
        EntityID:     entityID,
        UploadedAt:   time.Now(),
    }

    err = s.db.Create(&attachment).Error
    if err != nil {
        // Clean up file if database save fails
        os.Remove(filepath.Join(uploadDir, uniqueFilename))
        return nil, err
    }

    return &UploadResult{
        ID:       attachment.ID,
        Filename: attachment.Filename,
        URL:      s.generateFileURL(entityType, uniqueFilename),
        Size:     written,
        MimeType: attachment.MimeType,
        Hash:     fileHash,
    }, nil
}

func (s *UploadService) DeleteFile(attachmentID uint, userID uint) error {
    var attachment models.Attachment
    err := s.db.Where("id = ? AND user_id = ?", attachmentID, userID).First(&attachment).Error
    if err != nil {
        return err
    }

    // Delete file from filesystem
    filePath := filepath.Join(s.uploadPath, attachment.EntityType, attachment.StoredName)
    err = os.Remove(filePath)
    if err != nil && !os.IsNotExist(err) {
        return err
    }

    // Delete record from database
    return s.db.Delete(&attachment).Error
}

func (s *UploadService) GetFilesByEntity(entityType string, entityID uint) ([]models.Attachment, error) {
    var attachments []models.Attachment
    err := s.db.Where("entity_type = ? AND entity_id = ?", entityType, entityID).Find(&attachments).Error
    return attachments, err
}

func (s *UploadService) isAllowedFileType(mimeType string) bool {
    allowedTypes := []string{
        "image/jpeg",
        "image/png",
        "image/gif",
        "image/webp",
        "application/pdf",
        "text/plain",
        "application/msword",
        "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
        "application/vnd.ms-excel",
        "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
    }

    for _, allowed := range allowedTypes {
        if mimeType == allowed {
            return true
        }
    }
    return false
}

func (s *UploadService) generateUniqueFilename(originalFilename string) string {
    ext := filepath.Ext(originalFilename)
    name := strings.TrimSuffix(originalFilename, ext)
    timestamp := time.Now().Unix()

    // Sanitize filename
    name = strings.ReplaceAll(name, " ", "_")
    name = strings.ToLower(name)

    return fmt.Sprintf("%s_%d%s", name, timestamp, ext)
}

func (s *UploadService) generateFileURL(entityType, filename string) string {
    return fmt.Sprintf("/uploads/%s/%s", entityType, filename)
}
```

### File Upload Component (client/components/common/FileUpload.tsx)

```typescript
import React, { useState, useCallback } from 'react';
import { View, TouchableOpacity, Alert, StyleSheet } from 'react-native';
import * as DocumentPicker from 'expo-document-picker';
import * as ImagePicker from 'expo-image-picker';
import { Ionicons } from '@expo/vector-icons';
import ThemedText from '../ui/themed-text';
import ThemedView from '../ui/themed-view';
import Button from '../ui/button';

interface FileUploadProps {
  onUpload: (file: DocumentPicker.DocumentResult) => Promise<void>;
  acceptedTypes?: string[];
  maxSize?: number;
  allowCamera?: boolean;
  multiple?: boolean;
  disabled?: boolean;
}

export default function FileUpload({
  onUpload,
  acceptedTypes = ['*/*'],
  maxSize = 10 * 1024 * 1024, // 10MB
  allowCamera = false,
  multiple = false,
  disabled = false,
}: FileUploadProps) {
  const [uploading, setUploading] = useState(false);

  const handleDocumentPick = useCallback(async () => {
    try {
      const result = await DocumentPicker.getDocumentAsync({
        type: acceptedTypes,
        multiple,
        copyToCacheDirectory: true,
      });

      if (result.type === 'success') {
        if (result.size && result.size > maxSize) {
          Alert.alert('Error', `File size must be less than ${maxSize / 1024 / 1024}MB`);
          return;
        }

        setUploading(true);
        await onUpload(result);
      }
    } catch (error) {
      Alert.alert('Error', 'Failed to select file');
    } finally {
      setUploading(false);
    }
  }, [acceptedTypes, multiple, maxSize, onUpload]);

  const handleImagePick = useCallback(async () => {
    const permissionResult = await ImagePicker.requestMediaLibraryPermissionsAsync();

    if (permissionResult.granted === false) {
      Alert.alert('Permission Required', 'Please allow access to your photo library');
      return;
    }

    try {
      const result = await ImagePicker.launchImageLibraryAsync({
        mediaTypes: ImagePicker.MediaTypeOptions.Images,
        allowsEditing: true,
        aspect: [4, 3],
        quality: 0.8,
      });

      if (!result.canceled && result.assets[0]) {
        const asset = result.assets[0];

        // Convert to DocumentResult format
        const file = {
          type: 'success' as const,
          name: asset.fileName || 'image.jpg',
          uri: asset.uri,
          size: asset.fileSize,
          mimeType: 'image/jpeg',
        };

        if (file.size && file.size > maxSize) {
          Alert.alert('Error', `File size must be less than ${maxSize / 1024 / 1024}MB`);
          return;
        }

        setUploading(true);
        await onUpload(file);
      }
    } catch (error) {
      Alert.alert('Error', 'Failed to select image');
    } finally {
      setUploading(false);
    }
  }, [maxSize, onUpload]);

  const handleCameraPick = useCallback(async () => {
    const permissionResult = await ImagePicker.requestCameraPermissionsAsync();

    if (permissionResult.granted === false) {
      Alert.alert('Permission Required', 'Please allow access to your camera');
      return;
    }

    try {
      const result = await ImagePicker.launchCameraAsync({
        allowsEditing: true,
        aspect: [4, 3],
        quality: 0.8,
      });

      if (!result.canceled && result.assets[0]) {
        const asset = result.assets[0];

        const file = {
          type: 'success' as const,
          name: 'camera_image.jpg',
          uri: asset.uri,
          size: asset.fileSize,
          mimeType: 'image/jpeg',
        };

        if (file.size && file.size > maxSize) {
          Alert.alert('Error', `File size must be less than ${maxSize / 1024 / 1024}MB`);
          return;
        }

        setUploading(true);
        await onUpload(file);
      }
    } catch (error) {
      Alert.alert('Error', 'Failed to take photo');
    } finally {
      setUploading(false);
    }
  }, [maxSize, onUpload]);

  const showUploadOptions = () => {
    const options = ['Choose File'];

    if (acceptedTypes.includes('image/*') || acceptedTypes.includes('*/*')) {
      options.push('Choose Photo');
      if (allowCamera) {
        options.push('Take Photo');
      }
    }

    options.push('Cancel');

    Alert.alert(
      'Upload File',
      'Choose an option',
      options.map((option, index) => ({
        text: option,
        onPress: () => {
          switch (index) {
            case 0:
              handleDocumentPick();
              break;
            case 1:
              if (options[1] === 'Choose Photo') {
                handleImagePick();
              }
              break;
            case 2:
              if (options[2] === 'Take Photo') {
                handleCameraPick();
              }
              break;
          }
        },
        style: option === 'Cancel' ? 'cancel' : 'default',
      }))
    );
  };

  return (
    <ThemedView style={styles.container}>
      <Button
        variant="secondary"
        onPress={showUploadOptions}
        disabled={disabled || uploading}
        loading={uploading}
        leftIcon={<Ionicons name="cloud-upload-outline" size={20} />}
      >
        {uploading ? 'Uploading...' : 'Upload File'}
      </Button>

      <ThemedText variant="caption" color="secondary" style={styles.hint}>
        Max size: {Math.round(maxSize / 1024 / 1024)}MB
      </ThemedText>
    </ThemedView>
  );
}

const styles = StyleSheet.create({
  container: {
    alignItems: 'center',
  },
  hint: {
    marginTop: 4,
    textAlign: 'center',
  },
});
```

## Push Notifications

### Push Notification Service (server/internal/services/push.go)

```go
package services

import (
    "bytes"
    "encoding/json"
    "fmt"
    "net/http"
    "time"

    "gorm.io/gorm"
    "quester-server/internal/models"
)

type PushNotificationService struct {
    db           *gorm.DB
    expoAPIURL   string
    accessToken  string
}

type ExpoPushMessage struct {
    To       []string               `json:"to"`
    Title    string                 `json:"title"`
    Body     string                 `json:"body"`
    Data     map[string]interface{} `json:"data,omitempty"`
    Sound    string                 `json:"sound,omitempty"`
    Priority string                 `json:"priority,omitempty"`
    Badge    *int                   `json:"badge,omitempty"`
}

type ExpoResponse struct {
    Data []struct {
        Status string `json:"status"`
        ID     string `json:"id,omitempty"`
        Error  string `json:"message,omitempty"`
    } `json:"data"`
}

func NewPushNotificationService(db *gorm.DB, accessToken string) *PushNotificationService {
    return &PushNotificationService{
        db:          db,
        expoAPIURL:  "https://exp.host/--/api/v2/push/send",
        accessToken: accessToken,
    }
}

func (s *PushNotificationService) SendNotification(userID uint, title, body string, data map[string]interface{}) error {
    // Get user's push tokens
    var pushTokens []models.PushToken
    err := s.db.Where("user_id = ? AND is_active = ?", userID, true).Find(&pushTokens).Error
    if err != nil {
        return err
    }

    if len(pushTokens) == 0 {
        return nil // User has no push tokens
    }

    // Prepare tokens
    var tokens []string
    for _, token := range pushTokens {
        tokens = append(tokens, token.Token)
    }

    // Create push message
    message := ExpoPushMessage{
        To:       tokens,
        Title:    title,
        Body:     body,
        Data:     data,
        Sound:    "default",
        Priority: "default",
    }

    // Send to Expo
    return s.sendToExpo(message)
}

func (s *PushNotificationService) SendQuestNotification(questID uint, title, body string) error {
    // Get quest participants
    var participants []models.User
    err := s.db.Table("users").
        Joins("JOIN quest_participants ON users.id = quest_participants.user_id").
        Where("quest_participants.quest_id = ?", questID).
        Find(&participants).Error
    if err != nil {
        return err
    }

    // Send to all participants
    for _, user := range participants {
        data := map[string]interface{}{
            "type":     "quest_update",
            "quest_id": questID,
        }

        err = s.SendNotification(user.ID, title, body, data)
        if err != nil {
            // Log error but continue sending to other users
            fmt.Printf("Failed to send notification to user %d: %v\n", user.ID, err)
        }
    }

    return nil
}

func (s *PushNotificationService) sendToExpo(message ExpoPushMessage) error {
    jsonData, err := json.Marshal(message)
    if err != nil {
        return err
    }

    req, err := http.NewRequest("POST", s.expoAPIURL, bytes.NewBuffer(jsonData))
    if err != nil {
        return err
    }

    req.Header.Set("Content-Type", "application/json")
    req.Header.Set("Accept", "application/json")
    if s.accessToken != "" {
        req.Header.Set("Authorization", "Bearer "+s.accessToken)
    }

    client := &http.Client{Timeout: 30 * time.Second}
    resp, err := client.Do(req)
    if err != nil {
        return err
    }
    defer resp.Body.Close()

    if resp.StatusCode != http.StatusOK {
        return fmt.Errorf("expo API returned status %d", resp.StatusCode)
    }

    var expoResp ExpoResponse
    err = json.NewDecoder(resp.Body).Decode(&expoResp)
    if err != nil {
        return err
    }

    // Check for errors in response
    for _, result := range expoResp.Data {
        if result.Status == "error" {
            return fmt.Errorf("expo push error: %s", result.Error)
        }
    }

    return nil
}

func (s *PushNotificationService) RegisterPushToken(userID uint, token string, deviceType string) error {
    // Check if token already exists
    var existingToken models.PushToken
    err := s.db.Where("token = ?", token).First(&existingToken).Error

    if err == gorm.ErrRecordNotFound {
        // Create new token
        pushToken := models.PushToken{
            UserID:     userID,
            Token:      token,
            DeviceType: deviceType,
            IsActive:   true,
            CreatedAt:  time.Now(),
        }
        return s.db.Create(&pushToken).Error
    } else if err != nil {
        return err
    } else {
        // Update existing token
        existingToken.UserID = userID
        existingToken.IsActive = true
        existingToken.UpdatedAt = time.Now()
        return s.db.Save(&existingToken).Error
    }
}
```

This advanced features implementation provides comprehensive functionality for gamification, search, file handling, and notifications, making the Quester application feature-rich and engaging for users.