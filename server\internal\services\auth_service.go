package services

import (
	"errors"
	"fmt"
	"time"

	"gorm.io/gorm"
	"quester-server/internal/models"
	"quester-server/internal/utils"
)

// AuthService handles authentication business logic
type AuthService struct {
	db         *gorm.DB
	jwtManager *utils.JWTManager
}

// NewAuthService creates a new authentication service
func NewAuthService(db *gorm.DB, jwtManager *utils.JWTManager) *AuthService {
	return &AuthService{
		db:         db,
		jwtManager: jwtManager,
	}
}

// AuthResponse represents the authentication response
type AuthResponse struct {
	User         models.UserResponse `json:"user"`
	AccessToken  string             `json:"access_token"`
	RefreshToken string             `json:"refresh_token"`
	ExpiresIn    int64              `json:"expires_in"` // Access token expiry in seconds
}

// Register creates a new user account
func (s *AuthService) Register(req models.UserRequest) (*AuthResponse, error) {
	// Validate request
	if err := req.Validate(); err != nil {
		return nil, fmt.Errorf("validation failed: %w", err)
	}

	// Check if user already exists
	var existingUser models.User
	if err := s.db.Where("email = ? OR username = ?", req.Email, req.Username).First(&existingUser).Error; err == nil {
		return nil, errors.New("user with this email or username already exists")
	} else if !errors.Is(err, gorm.ErrRecordNotFound) {
		return nil, fmt.Errorf("database error: %w", err)
	}

	// Hash password
	hashedPassword, err := utils.HashPassword(req.Password)
	if err != nil {
		return nil, fmt.Errorf("failed to hash password: %w", err)
	}

	// Create user
	user := models.User{
		Username:     req.Username,
		Email:        req.Email,
		FirstName:    req.FirstName,
		LastName:     req.LastName,
		PasswordHash: hashedPassword,
		Role:         models.RoleUser,
		IsActive:     true,
		IsVerified:   false, // Set to false initially, require email verification
		Level:        1,
		TotalPoints:  0,
	}

	// Begin transaction
	tx := s.db.Begin()
	if tx.Error != nil {
		return nil, fmt.Errorf("failed to begin transaction: %w", tx.Error)
	}

	// Create user
	if err := tx.Create(&user).Error; err != nil {
		tx.Rollback()
		return nil, fmt.Errorf("failed to create user: %w", err)
	}

	// Create user stats
	userStats := models.UserStats{
		UserID: user.ID,
	}
	if err := tx.Create(&userStats).Error; err != nil {
		tx.Rollback()
		return nil, fmt.Errorf("failed to create user stats: %w", err)
	}

	// Create user preferences
	userPrefs := models.UserPreference{
		UserID: user.ID,
	}
	if err := tx.Create(&userPrefs).Error; err != nil {
		tx.Rollback()
		return nil, fmt.Errorf("failed to create user preferences: %w", err)
	}

	// Commit transaction
	if err := tx.Commit().Error; err != nil {
		return nil, fmt.Errorf("failed to commit transaction: %w", err)
	}

	// Generate tokens
	accessToken, refreshToken, err := s.jwtManager.GenerateTokenPair(
		user.ID, user.Username, user.Email, string(user.Role),
	)
	if err != nil {
		return nil, fmt.Errorf("failed to generate tokens: %w", err)
	}

	// Log user activity
	s.logActivity(user.ID, "user_registered", "User account created", "", "")

	return &AuthResponse{
		User:         user.ToResponse(),
		AccessToken:  accessToken,
		RefreshToken: refreshToken,
		ExpiresIn:    int64(s.jwtManager.GetTokenExpiry(utils.AccessToken).Seconds()),
	}, nil
}

// Login authenticates a user and returns tokens
func (s *AuthService) Login(email, password, ipAddress, userAgent string) (*AuthResponse, error) {
	// Find user by email
	var user models.User
	if err := s.db.Where("email = ?", email).First(&user).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("invalid email or password")
		}
		return nil, fmt.Errorf("database error: %w", err)
	}

	// Check if user is active
	if !user.IsActive {
		return nil, errors.New("account is deactivated")
	}

	// Verify password
	if err := utils.VerifyPassword(user.PasswordHash, password); err != nil {
		return nil, errors.New("invalid email or password")
	}

	// Update last login time
	now := time.Now()
	user.LastLoginAt = &now
	if err := s.db.Save(&user).Error; err != nil {
		return nil, fmt.Errorf("failed to update last login: %w", err)
	}

	// Generate tokens
	accessToken, refreshToken, err := s.jwtManager.GenerateTokenPair(
		user.ID, user.Username, user.Email, string(user.Role),
	)
	if err != nil {
		return nil, fmt.Errorf("failed to generate tokens: %w", err)
	}

	// Create user session
	session := models.UserSession{
		UserID:    user.ID,
		Token:     refreshToken, // Store refresh token hash in production
		ExpiresAt: time.Now().Add(s.jwtManager.GetTokenExpiry(utils.RefreshToken)),
		IsActive:  true,
		IPAddress: ipAddress,
		UserAgent: userAgent,
	}
	if err := s.db.Create(&session).Error; err != nil {
		// Don't fail login if session creation fails, just log the error
		fmt.Printf("Failed to create user session: %v\n", err)
	}

	// Log user activity
	s.logActivity(user.ID, "user_login", "User logged in", ipAddress, userAgent)

	return &AuthResponse{
		User:         user.ToResponse(),
		AccessToken:  accessToken,
		RefreshToken: refreshToken,
		ExpiresIn:    int64(s.jwtManager.GetTokenExpiry(utils.AccessToken).Seconds()),
	}, nil
}

// RefreshTokens validates refresh token and generates new token pair
func (s *AuthService) RefreshTokens(refreshToken string) (*AuthResponse, error) {
	// Validate refresh token
	claims, err := s.jwtManager.ValidateRefreshToken(refreshToken)
	if err != nil {
		return nil, fmt.Errorf("invalid refresh token: %w", err)
	}

	// Get user from database to ensure they still exist and are active
	var user models.User
	if err := s.db.Where("id = ?", claims.UserID).First(&user).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("user not found")
		}
		return nil, fmt.Errorf("database error: %w", err)
	}

	// Check if user is still active
	if !user.IsActive {
		return nil, errors.New("account is deactivated")
	}

	// Generate new token pair
	accessToken, newRefreshToken, err := s.jwtManager.GenerateTokenPair(
		user.ID, user.Username, user.Email, string(user.Role),
	)
	if err != nil {
		return nil, fmt.Errorf("failed to generate tokens: %w", err)
	}

	// Update session with new refresh token (in production, invalidate old refresh token)
	if err := s.db.Model(&models.UserSession{}).
		Where("user_id = ? AND token = ?", user.ID, refreshToken).
		Update("token", newRefreshToken).Error; err != nil {
		// Don't fail if session update fails
		fmt.Printf("Failed to update user session: %v\n", err)
	}

	return &AuthResponse{
		User:         user.ToResponse(),
		AccessToken:  accessToken,
		RefreshToken: newRefreshToken,
		ExpiresIn:    int64(s.jwtManager.GetTokenExpiry(utils.AccessToken).Seconds()),
	}, nil
}

// GetUserByID retrieves a user by ID
func (s *AuthService) GetUserByID(userID uint) (*models.User, error) {
	var user models.User
	if err := s.db.Where("id = ?", userID).First(&user).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("user not found")
		}
		return nil, fmt.Errorf("database error: %w", err)
	}
	return &user, nil
}

// UpdateUser updates user profile information
func (s *AuthService) UpdateUser(userID uint, req models.UserUpdateRequest) (*models.User, error) {
	// Validate request
	if err := req.Validate(); err != nil {
		return nil, fmt.Errorf("validation failed: %w", err)
	}

	// Get user
	user, err := s.GetUserByID(userID)
	if err != nil {
		return nil, err
	}

	// Update fields if provided
	if req.FirstName != nil {
		user.FirstName = *req.FirstName
	}
	if req.LastName != nil {
		user.LastName = *req.LastName
	}
	if req.Bio != nil {
		user.Bio = *req.Bio
	}
	if req.Avatar != nil {
		user.Avatar = *req.Avatar
	}

	// Save user
	if err := s.db.Save(user).Error; err != nil {
		return nil, fmt.Errorf("failed to update user: %w", err)
	}

	// Log activity
	s.logActivity(user.ID, "profile_updated", "User profile updated", "", "")

	return user, nil
}

// ChangePassword changes user password
func (s *AuthService) ChangePassword(userID uint, req models.ChangePasswordRequest) error {
	// Validate request
	if err := req.Validate(); err != nil {
		return fmt.Errorf("validation failed: %w", err)
	}

	// Get user
	user, err := s.GetUserByID(userID)
	if err != nil {
		return err
	}

	// Verify current password
	if err := utils.VerifyPassword(user.PasswordHash, req.CurrentPassword); err != nil {
		return errors.New("current password is incorrect")
	}

	// Hash new password
	hashedPassword, err := utils.HashPassword(req.NewPassword)
	if err != nil {
		return fmt.Errorf("failed to hash new password: %w", err)
	}

	// Update password
	user.PasswordHash = hashedPassword
	if err := s.db.Save(user).Error; err != nil {
		return fmt.Errorf("failed to update password: %w", err)
	}

	// Invalidate all user sessions (force re-login)
	if err := s.db.Model(&models.UserSession{}).
		Where("user_id = ?", userID).
		Update("is_active", false).Error; err != nil {
		// Don't fail password change if session invalidation fails
		fmt.Printf("Failed to invalidate user sessions: %v\n", err)
	}

	// Log activity
	s.logActivity(user.ID, "password_changed", "User password changed", "", "")

	return nil
}

// Logout invalidates user session
func (s *AuthService) Logout(userID uint, refreshToken string) error {
	// Invalidate the specific session
	if err := s.db.Model(&models.UserSession{}).
		Where("user_id = ? AND token = ?", userID, refreshToken).
		Update("is_active", false).Error; err != nil {
		return fmt.Errorf("failed to invalidate session: %w", err)
	}

	// Log activity
	s.logActivity(userID, "user_logout", "User logged out", "", "")

	return nil
}

// LogoutAll invalidates all user sessions
func (s *AuthService) LogoutAll(userID uint) error {
	// Invalidate all user sessions
	if err := s.db.Model(&models.UserSession{}).
		Where("user_id = ?", userID).
		Update("is_active", false).Error; err != nil {
		return fmt.Errorf("failed to invalidate all sessions: %w", err)
	}

	// Log activity
	s.logActivity(userID, "user_logout_all", "User logged out from all devices", "", "")

	return nil
}

// logActivity logs user activity
func (s *AuthService) logActivity(userID uint, action, description, ipAddress, userAgent string) {
	activityLog := models.ActivityLog{
		UserID:      userID,
		Action:      action,
		Description: description,
		IPAddress:   ipAddress,
		UserAgent:   userAgent,
	}

	if err := s.db.Create(&activityLog).Error; err != nil {
		// Log error but don't fail the main operation
		fmt.Printf("Failed to log activity: %v\n", err)
	}
}