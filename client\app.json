{"expo": {"name": "client", "slug": "client", "version": "1.0.0", "orientation": "portrait", "icon": "./assets/images/icon.png", "scheme": "client", "userInterfaceStyle": "automatic", "newArchEnabled": true, "splash": {"image": "./assets/images/splash.png", "resizeMode": "contain", "backgroundColor": "#ffffff"}, "assetBundlePatterns": ["**/*"], "ios": {"supportsTablet": true}, "android": {"edgeToEdgeEnabled": true, "adaptiveIcon": {"foregroundImage": "./assets/images/adaptive-icon.png", "backgroundColor": "#ffffff"}}, "web": {"bundler": "metro", "output": "static", "favicon": "./assets/images/favicon.png"}, "plugins": ["expo-router"], "experiments": {"typedRoutes": true}}}