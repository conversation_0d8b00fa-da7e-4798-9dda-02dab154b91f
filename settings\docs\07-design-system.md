# Design System & UI Standards

## Color Palette

### Primary Colors
```typescript
export const Colors = {
  // Primary brand colors
  primary: {
    50: '#eff6ff',
    100: '#dbeafe',
    200: '#bfdbfe',
    300: '#93c5fd',
    400: '#60a5fa',
    500: '#3b82f6',  // Main brand color
    600: '#2563eb',
    700: '#1d4ed8',
    800: '#1e40af',
    900: '#1e3a8a',
  },

  // Secondary colors
  secondary: {
    50: '#f0f9ff',
    100: '#e0f2fe',
    200: '#bae6fd',
    300: '#7dd3fc',
    400: '#38bdf8',
    500: '#0ea5e9',
    600: '#0284c7',
    700: '#0369a1',
    800: '#075985',
    900: '#0c4a6e',
  },

  // Status colors
  success: {
    50: '#f0fdf4',
    500: '#22c55e',
    600: '#16a34a',
  },
  warning: {
    50: '#fffbeb',
    500: '#f59e0b',
    600: '#d97706',
  },
  error: {
    50: '#fef2f2',
    500: '#ef4444',
    600: '#dc2626',
  },
  info: {
    50: '#eff6ff',
    500: '#3b82f6',
    600: '#2563eb',
  },
};
```

### Theme Colors (Light/Dark)
```typescript
export const lightTheme = {
  // Background colors
  background: {
    primary: '#ffffff',
    secondary: '#f8fafc',
    tertiary: '#f1f5f9',
  },

  // Surface colors
  surface: {
    primary: '#ffffff',
    secondary: '#f8fafc',
    elevated: '#ffffff',
  },

  // Text colors
  text: {
    primary: '#0f172a',
    secondary: '#475569',
    tertiary: '#64748b',
    disabled: '#94a3b8',
    inverse: '#ffffff',
  },

  // Border colors
  border: {
    light: '#e2e8f0',
    medium: '#cbd5e1',
    dark: '#94a3b8',
  },
};

export const darkTheme = {
  // Background colors
  background: {
    primary: '#0f172a',
    secondary: '#1e293b',
    tertiary: '#334155',
  },

  // Surface colors
  surface: {
    primary: '#1e293b',
    secondary: '#334155',
    elevated: '#475569',
  },

  // Text colors
  text: {
    primary: '#f8fafc',
    secondary: '#cbd5e1',
    tertiary: '#94a3b8',
    disabled: '#64748b',
    inverse: '#0f172a',
  },

  // Border colors
  border: {
    light: '#334155',
    medium: '#475569',
    dark: '#64748b',
  },
};
```

## Typography System

### Font Scale
```typescript
export const Typography = {
  // Font families
  fonts: {
    primary: 'System', // Default system font
    mono: 'SF Mono, Monaco, Consolas, monospace',
  },

  // Font sizes
  sizes: {
    xs: 12,
    sm: 14,
    base: 16,
    lg: 18,
    xl: 20,
    '2xl': 24,
    '3xl': 30,
    '4xl': 36,
    '5xl': 48,
    '6xl': 60,
  },

  // Line heights
  lineHeights: {
    tight: 1.25,
    normal: 1.5,
    relaxed: 1.625,
  },

  // Font weights
  weights: {
    light: '300',
    normal: '400',
    medium: '500',
    semibold: '600',
    bold: '700',
  },
};
```

### Text Styles
```typescript
export const textStyles = {
  // Headings
  h1: {
    fontSize: Typography.sizes['4xl'],
    fontWeight: Typography.weights.bold,
    lineHeight: Typography.lineHeights.tight,
  },
  h2: {
    fontSize: Typography.sizes['3xl'],
    fontWeight: Typography.weights.semibold,
    lineHeight: Typography.lineHeights.tight,
  },
  h3: {
    fontSize: Typography.sizes['2xl'],
    fontWeight: Typography.weights.semibold,
    lineHeight: Typography.lineHeights.normal,
  },
  h4: {
    fontSize: Typography.sizes.xl,
    fontWeight: Typography.weights.medium,
    lineHeight: Typography.lineHeights.normal,
  },

  // Body text
  body: {
    fontSize: Typography.sizes.base,
    fontWeight: Typography.weights.normal,
    lineHeight: Typography.lineHeights.normal,
  },
  bodySmall: {
    fontSize: Typography.sizes.sm,
    fontWeight: Typography.weights.normal,
    lineHeight: Typography.lineHeights.normal,
  },

  // Captions
  caption: {
    fontSize: Typography.sizes.xs,
    fontWeight: Typography.weights.normal,
    lineHeight: Typography.lineHeights.normal,
  },

  // Special text
  button: {
    fontSize: Typography.sizes.base,
    fontWeight: Typography.weights.medium,
    lineHeight: Typography.lineHeights.tight,
  },
  label: {
    fontSize: Typography.sizes.sm,
    fontWeight: Typography.weights.medium,
    lineHeight: Typography.lineHeights.normal,
  },
};
```

## Spacing System

### Spacing Scale
```typescript
export const Spacing = {
  0: 0,
  1: 4,    // 0.25rem
  2: 8,    // 0.5rem
  3: 12,   // 0.75rem
  4: 16,   // 1rem
  5: 20,   // 1.25rem
  6: 24,   // 1.5rem
  8: 32,   // 2rem
  10: 40,  // 2.5rem
  12: 48,  // 3rem
  16: 64,  // 4rem
  20: 80,  // 5rem
  24: 96,  // 6rem
};

// Component spacing
export const ComponentSpacing = {
  // Padding
  padding: {
    xs: Spacing[2],
    sm: Spacing[3],
    md: Spacing[4],
    lg: Spacing[6],
    xl: Spacing[8],
  },

  // Margins
  margin: {
    xs: Spacing[2],
    sm: Spacing[4],
    md: Spacing[6],
    lg: Spacing[8],
    xl: Spacing[12],
  },

  // Gaps
  gap: {
    xs: Spacing[1],
    sm: Spacing[2],
    md: Spacing[4],
    lg: Spacing[6],
    xl: Spacing[8],
  },
};
```

## Component Standards

### Border Radius
```typescript
export const BorderRadius = {
  none: 0,
  sm: 4,
  md: 8,
  lg: 12,
  xl: 16,
  full: 9999,
};
```

### Shadows
```typescript
export const Shadows = {
  none: 'none',
  sm: '0 1px 2px 0 rgba(0, 0, 0, 0.05)',
  base: '0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06)',
  md: '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)',
  lg: '0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)',
  xl: '0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)',
};
```

### Button Variants
```typescript
export const buttonVariants = {
  // Primary button
  primary: {
    backgroundColor: Colors.primary[500],
    color: lightTheme.text.inverse,
    borderRadius: BorderRadius.md,
    paddingVertical: ComponentSpacing.padding.md,
    paddingHorizontal: ComponentSpacing.padding.lg,
    ...textStyles.button,
  },

  // Secondary button
  secondary: {
    backgroundColor: 'transparent',
    color: Colors.primary[500],
    borderWidth: 1,
    borderColor: Colors.primary[500],
    borderRadius: BorderRadius.md,
    paddingVertical: ComponentSpacing.padding.md,
    paddingHorizontal: ComponentSpacing.padding.lg,
    ...textStyles.button,
  },

  // Ghost button
  ghost: {
    backgroundColor: 'transparent',
    color: Colors.primary[500],
    borderRadius: BorderRadius.md,
    paddingVertical: ComponentSpacing.padding.md,
    paddingHorizontal: ComponentSpacing.padding.lg,
    ...textStyles.button,
  },

  // Destructive button
  destructive: {
    backgroundColor: Colors.error[500],
    color: lightTheme.text.inverse,
    borderRadius: BorderRadius.md,
    paddingVertical: ComponentSpacing.padding.md,
    paddingHorizontal: ComponentSpacing.padding.lg,
    ...textStyles.button,
  },
};
```

### Input Variants
```typescript
export const inputVariants = {
  // Default input
  default: {
    borderWidth: 1,
    borderColor: lightTheme.border.medium,
    borderRadius: BorderRadius.md,
    paddingVertical: ComponentSpacing.padding.md,
    paddingHorizontal: ComponentSpacing.padding.md,
    backgroundColor: lightTheme.surface.primary,
    color: lightTheme.text.primary,
    fontSize: Typography.sizes.base,
  },

  // Error state
  error: {
    borderColor: Colors.error[500],
    backgroundColor: Colors.error[50],
  },

  // Focus state
  focus: {
    borderColor: Colors.primary[500],
    borderWidth: 2,
  },

  // Disabled state
  disabled: {
    backgroundColor: lightTheme.background.secondary,
    color: lightTheme.text.disabled,
    borderColor: lightTheme.border.light,
  },
};
```

## Iconography

### Icon Sizes
```typescript
export const IconSizes = {
  xs: 12,
  sm: 16,
  md: 20,
  lg: 24,
  xl: 32,
  '2xl': 40,
};
```

### Icon Usage Guidelines
- Use Expo Vector Icons (Ionicons) for consistency
- Primary icons: outline style for inactive states
- Secondary icons: filled style for active states
- Status icons: use appropriate colors (success, warning, error)

## Layout Standards

### Container Widths
```typescript
export const ContainerWidths = {
  sm: 640,
  md: 768,
  lg: 1024,
  xl: 1280,
  full: '100%',
};
```

### Grid System
```typescript
export const Grid = {
  columns: 12,
  gutters: {
    xs: Spacing[2],
    sm: Spacing[4],
    md: Spacing[6],
    lg: Spacing[8],
  },
};
```

## Component Hierarchy

### Z-Index Scale
```typescript
export const ZIndex = {
  base: 0,
  above: 1,
  dropdown: 10,
  overlay: 20,
  modal: 30,
  toast: 40,
  tooltip: 50,
};
```

## Animation Standards

### Timing Functions
```typescript
export const Animations = {
  // Durations (milliseconds)
  duration: {
    fast: 150,
    normal: 300,
    slow: 500,
  },

  // Easing functions
  easing: {
    linear: 'linear',
    ease: 'ease',
    easeIn: 'ease-in',
    easeOut: 'ease-out',
    easeInOut: 'ease-in-out',
  },
};
```

## Accessibility Standards

### Focus States
- All interactive elements must have visible focus indicators
- Focus indicators should use the primary color
- Minimum touch target size: 44x44 points

### Color Contrast
- Normal text: minimum 4.5:1 contrast ratio
- Large text: minimum 3:1 contrast ratio
- UI components: minimum 3:1 contrast ratio

### Screen Reader Support
- All images must have alt text
- All buttons must have accessible labels
- Form inputs must have associated labels

## Usage Guidelines

### Color Usage
- Use primary colors for main actions and branding
- Use secondary colors for supporting actions
- Use status colors for feedback and states
- Maintain sufficient contrast for accessibility

### Typography Usage
- Use heading hierarchy consistently (h1 > h2 > h3 > h4)
- Maintain readable line lengths (45-75 characters)
- Use appropriate font weights for emphasis

### Spacing Usage
- Use consistent spacing multiples (4, 8, 12, 16, etc.)
- Maintain visual rhythm with consistent spacing
- Use larger spacing for component separation

This design system ensures visual consistency and provides clear guidelines for implementing the Quester UI across all platforms.