package routes

import (
	"net/http"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"

	"quester-server/internal/config"
	"quester-server/internal/handlers"
	"quester-server/internal/middleware"
	"quester-server/internal/websocket"
)

// SetupRoutes configures all application routes
func SetupRoutes(router *gin.Engine, db *gorm.DB, hub *websocket.Hub, cfg *config.Config) {
	// Initialize handlers
	authHandler := handlers.NewAuthHandler(db, cfg)
	userHandler := handlers.NewUserHandler(db, cfg)
	questHandler := handlers.NewQuestHandler(db, cfg)
	taskHandler := handlers.NewTaskHandler(db, cfg)
	rewardHandler := handlers.NewRewardHandler(db, cfg)
	categoryHandler := handlers.NewCategoryHandler(db, cfg)
	tagHandler := handlers.NewTagHandler(db, cfg)
	commentHandler := handlers.NewCommentHandler(db, cfg)
	notificationHandler := handlers.NewNotificationHandler(db, cfg)
	attachmentHandler := handlers.NewAttachmentHandler(db, cfg)
	teamHandler := handlers.NewTeamHandler(db, cfg)
	analyticsHandler := handlers.NewAnalyticsHandler(db, cfg)
	adminHandler := handlers.NewAdminHandler(db, cfg)
	webhookHandler := handlers.NewWebhookHandler(db, cfg)

	// Initialize middleware
	authMiddleware := middleware.NewAuthMiddleware(db, cfg)
	rateLimitMiddleware := middleware.NewRateLimitMiddleware(cfg)
	loggingMiddleware := middleware.NewLoggingMiddleware(cfg)

	// Global middleware
	router.Use(loggingMiddleware.RequestLogger())

	if cfg.EnableRateLimit {
		router.Use(rateLimitMiddleware.RateLimit())
	}

	// Health check endpoint
	router.GET("/health", func(c *gin.Context) {
		c.JSON(http.StatusOK, gin.H{
			"status":  "ok",
			"service": "quester-api",
			"version": "1.0.0",
		})
	})

	// API version 1
	v1 := router.Group("/api/v1")
	{
		// Public routes (no authentication required)
		public := v1.Group("/")
		{
			// Authentication routes
			auth := public.Group("/auth")
			{
				auth.POST("/register", authHandler.Register)
				auth.POST("/login", authHandler.Login)
				auth.POST("/logout", authHandler.Logout)
				auth.POST("/refresh", authHandler.RefreshToken)
				auth.POST("/forgot-password", authHandler.ForgotPassword)
				auth.POST("/reset-password", authHandler.ResetPassword)
				auth.POST("/verify-email", authHandler.VerifyEmail)
				auth.POST("/resend-verification", authHandler.ResendVerification)
			}

			// Public API information
			public.GET("/info", func(c *gin.Context) {
				c.JSON(http.StatusOK, gin.H{
					"name":        "Quester API",
					"version":     "1.0.0",
					"description": "Goal tracking and achievement system API",
					"endpoints": gin.H{
						"auth":          "/api/v1/auth",
						"users":         "/api/v1/users",
						"quests":        "/api/v1/quests",
						"tasks":         "/api/v1/tasks",
						"rewards":       "/api/v1/rewards",
						"categories":    "/api/v1/categories",
						"tags":          "/api/v1/tags",
						"teams":         "/api/v1/teams",
						"notifications": "/api/v1/notifications",
						"websocket":     "/ws",
					},
				})
			})

			// Public categories and tags (for selection dropdowns)
			public.GET("/categories", categoryHandler.GetPublicCategories)
			public.GET("/tags", tagHandler.GetPublicTags)
		}

		// Protected routes (authentication required)
		protected := v1.Group("/")
		protected.Use(authMiddleware.RequireAuth())
		{
			// User routes
			users := protected.Group("/users")
			{
				users.GET("/me", userHandler.GetCurrentUser)
				users.PUT("/me", userHandler.UpdateCurrentUser)
				users.DELETE("/me", userHandler.DeleteCurrentUser)
				users.POST("/me/avatar", userHandler.UploadAvatar)
				users.GET("/me/stats", userHandler.GetUserStats)
				users.GET("/me/preferences", userHandler.GetUserPreferences)
				users.PUT("/me/preferences", userHandler.UpdateUserPreferences)
				users.GET("/me/activity", userHandler.GetUserActivity)

				// User search and discovery (if enabled)
				users.GET("/search", userHandler.SearchUsers)
				users.GET("/:id", userHandler.GetUserByID)
				users.GET("/:id/public-quests", questHandler.GetUserPublicQuests)
			}

			// Quest routes
			quests := protected.Group("/quests")
			{
				quests.GET("/", questHandler.GetQuests)
				quests.POST("/", questHandler.CreateQuest)
				quests.GET("/:id", questHandler.GetQuest)
				quests.PUT("/:id", questHandler.UpdateQuest)
				quests.DELETE("/:id", questHandler.DeleteQuest)
				quests.POST("/:id/start", questHandler.StartQuest)
				quests.POST("/:id/complete", questHandler.CompleteQuest)
				quests.POST("/:id/abandon", questHandler.AbandonQuest)
				quests.POST("/:id/share", questHandler.ShareQuest)
				quests.GET("/:id/stats", questHandler.GetQuestStats)
				quests.GET("/:id/comments", commentHandler.GetQuestComments)
				quests.POST("/:id/comments", commentHandler.CreateQuestComment)
				quests.GET("/:id/attachments", attachmentHandler.GetQuestAttachments)
				quests.POST("/:id/attachments", attachmentHandler.UploadQuestAttachment)
			}

			// Task routes
			tasks := protected.Group("/tasks")
			{
				tasks.GET("/", taskHandler.GetTasks)
				tasks.POST("/", taskHandler.CreateTask)
				tasks.GET("/:id", taskHandler.GetTask)
				tasks.PUT("/:id", taskHandler.UpdateTask)
				tasks.DELETE("/:id", taskHandler.DeleteTask)
				tasks.POST("/:id/complete", taskHandler.CompleteTask)
				tasks.POST("/:id/uncomplete", taskHandler.UncompleteTask)
				tasks.GET("/:id/comments", commentHandler.GetTaskComments)
				tasks.POST("/:id/comments", commentHandler.CreateTaskComment)
			}

			// Reward routes
			rewards := protected.Group("/rewards")
			{
				rewards.GET("/", rewardHandler.GetRewards)
				rewards.POST("/", rewardHandler.CreateReward)
				rewards.GET("/:id", rewardHandler.GetReward)
				rewards.PUT("/:id", rewardHandler.UpdateReward)
				rewards.DELETE("/:id", rewardHandler.DeleteReward)
				rewards.POST("/:id/claim", rewardHandler.ClaimReward)
				rewards.GET("/claimed", rewardHandler.GetClaimedRewards)
			}

			// Category routes
			categories := protected.Group("/categories")
			{
				categories.GET("/", categoryHandler.GetCategories)
				categories.POST("/", categoryHandler.CreateCategory)
				categories.GET("/:id", categoryHandler.GetCategory)
				categories.PUT("/:id", categoryHandler.UpdateCategory)
				categories.DELETE("/:id", categoryHandler.DeleteCategory)
			}

			// Tag routes
			tags := protected.Group("/tags")
			{
				tags.GET("/", tagHandler.GetTags)
				tags.POST("/", tagHandler.CreateTag)
				tags.GET("/:id", tagHandler.GetTag)
				tags.PUT("/:id", tagHandler.UpdateTag)
				tags.DELETE("/:id", tagHandler.DeleteTag)
			}

			// Team routes
			teams := protected.Group("/teams")
			{
				teams.GET("/", teamHandler.GetTeams)
				teams.POST("/", teamHandler.CreateTeam)
				teams.GET("/:id", teamHandler.GetTeam)
				teams.PUT("/:id", teamHandler.UpdateTeam)
				teams.DELETE("/:id", teamHandler.DeleteTeam)
				teams.POST("/:id/join", teamHandler.JoinTeam)
				teams.POST("/:id/leave", teamHandler.LeaveTeam)
				teams.GET("/:id/members", teamHandler.GetTeamMembers)
				teams.POST("/:id/members/:userId", teamHandler.AddTeamMember)
				teams.DELETE("/:id/members/:userId", teamHandler.RemoveTeamMember)
				teams.GET("/:id/quests", questHandler.GetTeamQuests)
				teams.POST("/:id/quests", questHandler.CreateTeamQuest)
			}

			// Notification routes
			notifications := protected.Group("/notifications")
			{
				notifications.GET("/", notificationHandler.GetNotifications)
				notifications.PUT("/:id/read", notificationHandler.MarkAsRead)
				notifications.PUT("/read-all", notificationHandler.MarkAllAsRead)
				notifications.DELETE("/:id", notificationHandler.DeleteNotification)
				notifications.GET("/unread-count", notificationHandler.GetUnreadCount)
			}

			// Analytics routes
			analytics := protected.Group("/analytics")
			{
				analytics.GET("/dashboard", analyticsHandler.GetDashboard)
				analytics.GET("/progress", analyticsHandler.GetProgress)
				analytics.GET("/habits", analyticsHandler.GetHabits)
				analytics.GET("/achievements", analyticsHandler.GetAchievements)
				analytics.GET("/time-tracking", analyticsHandler.GetTimeTracking)
			}

			// File attachment routes
			attachments := protected.Group("/attachments")
			{
				attachments.GET("/:id", attachmentHandler.GetAttachment)
				attachments.DELETE("/:id", attachmentHandler.DeleteAttachment)
				attachments.GET("/:id/download", attachmentHandler.DownloadAttachment)
			}
		}

		// Admin routes (admin authentication required)
		admin := v1.Group("/admin")
		admin.Use(authMiddleware.RequireAuth(), authMiddleware.RequireAdmin())
		{
			admin.GET("/users", adminHandler.GetUsers)
			admin.GET("/users/:id", adminHandler.GetUser)
			admin.PUT("/users/:id", adminHandler.UpdateUser)
			admin.DELETE("/users/:id", adminHandler.DeleteUser)
			admin.POST("/users/:id/ban", adminHandler.BanUser)
			admin.POST("/users/:id/unban", adminHandler.UnbanUser)

			admin.GET("/quests", adminHandler.GetQuests)
			admin.DELETE("/quests/:id", adminHandler.DeleteQuest)

			admin.GET("/reports", adminHandler.GetReports)
			admin.POST("/reports/:id/resolve", adminHandler.ResolveReport)

			admin.GET("/analytics", adminHandler.GetAnalytics)
			admin.GET("/system-health", adminHandler.GetSystemHealth)
		}

		// Webhook routes (for external integrations)
		webhooks := v1.Group("/webhooks")
		{
			webhooks.POST("/stripe", webhookHandler.StripeWebhook)
			webhooks.POST("/github", webhookHandler.GitHubWebhook)
			webhooks.POST("/slack", webhookHandler.SlackWebhook)
		}
	}

	// WebSocket endpoint
	router.GET("/ws", func(c *gin.Context) {
		websocket.HandleWebSocket(hub, c.Writer, c.Request)
	})

	// Static file serving (for uploaded files)
	if cfg.UploadPath != "" {
		router.Static("/uploads", cfg.UploadPath)
	}

	// Serve API documentation (if available)
	router.Static("/docs", "./docs")
	router.GET("/", func(c *gin.Context) {
		c.Redirect(http.StatusMovedPermanently, "/docs")
	})
}