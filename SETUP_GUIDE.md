# Quester Setup Guide

## Overview
Quester is a comprehensive quest management application with advanced gamification, learning management, and team collaboration features. This guide will help you set up and run both the backend server (Go) and frontend client (React Native Expo).

## Architecture Overview

### Backend (@server)
- **Framework**: Go with Fiber web framework
- **Database**: PostgreSQL with GORM ORM
- **Cache**: Redis for session management and caching
- **Authentication**: JWT with refresh tokens and session management
- **Features**: RBAC, Gamification, LMS, WebSocket support

### Frontend (@client)
- **Framework**: React Native with Expo
- **Navigation**: Expo Router
- **UI**: Custom glass-themed design system
- **State Management**: React hooks with optimistic updates
- **Features**: Interactive dashboard, animations, responsive design

## Prerequisites

### System Requirements
- **Node.js**: 18.0.0 or higher
- **Go**: 1.19 or higher
- **PostgreSQL**: 12.0 or higher
- **Redis**: 6.0 or higher
- **Git**: Latest version

### Development Tools
- **VS Code** (recommended) with Go and React Native extensions
- **Expo CLI**: `npm install -g @expo/cli`
- **Air** (for Go hot reload): `go install github.com/cosmtrek/air@latest`

## Database Setup

### PostgreSQL Installation

#### Windows (using PostgreSQL installer)
1. Download from https://www.postgresql.org/download/windows/
2. Install with default settings
3. Remember the superuser password

#### macOS (using Homebrew)
```bash
brew install postgresql
brew services start postgresql
```

#### Ubuntu/Debian
```bash
sudo apt update
sudo apt install postgresql postgresql-contrib
sudo systemctl start postgresql
sudo systemctl enable postgresql
```

### Database Configuration

1. **Create Database and User**:
```sql
-- Connect to PostgreSQL as superuser
psql -U postgres

-- Create database
CREATE DATABASE quester_db;

-- Create user
CREATE USER quester_user WITH PASSWORD 'your_secure_password';

-- Grant privileges
GRANT ALL PRIVILEGES ON DATABASE quester_db TO quester_user;

-- Exit
\q
```

2. **Test Connection**:
```bash
psql -U quester_user -d quester_db -h localhost
```

### Redis Installation

#### Windows
1. Download Redis for Windows from https://github.com/microsoftarchive/redis/releases
2. Install and start the service

#### macOS (using Homebrew)
```bash
brew install redis
brew services start redis
```

#### Ubuntu/Debian
```bash
sudo apt update
sudo apt install redis-server
sudo systemctl start redis-server
sudo systemctl enable redis-server
```

Test Redis connection:
```bash
redis-cli ping
# Should return: PONG
```

## Server Setup (@server)

### 1. Navigate to Server Directory
```bash
cd quester/server
```

### 2. Install Dependencies
```bash
go mod download
go mod tidy
```

### 3. Environment Configuration

Create `.env` file in the server root:
```env
# Database Configuration
DB_HOST=localhost
DB_PORT=5432
DB_USER=quester_user
DB_PASSWORD=your_secure_password
DB_NAME=quester_db
DB_SSLMODE=disable

# Redis Configuration
REDIS_ADDR=localhost:6379
REDIS_PASSWORD=
REDIS_DB=0

# JWT Configuration
JWT_SECRET=your-super-secret-jwt-key-change-this-in-production
JWT_REFRESH_SECRET=your-super-secret-refresh-key-change-this-in-production
JWT_EXPIRATION=15m
JWT_REFRESH_EXPIRATION=168h

# Server Configuration
PORT=8080
GIN_MODE=debug
CORS_ORIGINS=http://localhost:3000,http://localhost:19006

# Email Configuration (Optional - for production)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASSWORD=your-app-password

# File Upload Configuration
UPLOAD_MAX_SIZE=10485760  # 10MB
UPLOAD_ALLOWED_TYPES=image/jpeg,image/png,image/gif,application/pdf
```

### 4. Initialize Database

Run the application once to auto-migrate the database:
```bash
go run cmd/server/main.go
```

The application will:
- Auto-migrate all database tables
- Seed default roles and permissions
- Create a default admin user

### 5. Development with Hot Reload

Install Air for hot reload:
```bash
go install github.com/cosmtrek/air@latest
```

Create `.air.toml` configuration:
```toml
root = "."
testdata_dir = "testdata"
tmp_dir = "tmp"

[build]
args_bin = []
bin = "./tmp/main"
cmd = "go build -o ./tmp/main ./cmd/server/main.go"
delay = 1000
exclude_dir = ["assets", "tmp", "vendor", "testdata"]
exclude_file = []
exclude_regex = ["_test.go"]
exclude_unchanged = false
follow_symlink = false
full_bin = ""
include_dir = []
include_ext = ["go", "tpl", "tmpl", "html"]
include_file = []
kill_delay = "0s"
log = "build-errors.log"
poll = false
poll_interval = 0
rerun = false
rerun_delay = 500
send_interrupt = false
stop_on_root = false

[color]
app = ""
build = "yellow"
main = "magenta"
runner = "green"
watcher = "cyan"

[log]
main_only = false
time = false

[misc]
clean_on_exit = false

[screen]
clear_on_rebuild = false
keep_scroll = true
```

Start development server:
```bash
air
```

### 6. Available Server Scripts

Create useful scripts in the server directory:

**build.sh** (Linux/macOS):
```bash
#!/bin/bash
go build -o bin/server ./cmd/server/main.go
```

**run.sh** (Linux/macOS):
```bash
#!/bin/bash
go run cmd/server/main.go
```

**build.bat** (Windows):
```batch
go build -o bin/server.exe ./cmd/server/main.go
```

## Client Setup (@client)

### 1. Navigate to Client Directory
```bash
cd quester/client
```

### 2. Install Dependencies
```bash
npm install
```

### 3. Environment Configuration

Create `.env` file in the client root:
```env
# API Configuration
EXPO_PUBLIC_API_URL=http://localhost:8080/api/v1
EXPO_PUBLIC_WS_URL=ws://localhost:8080/ws

# App Configuration
EXPO_PUBLIC_APP_NAME=Quester
EXPO_PUBLIC_APP_VERSION=1.0.0
EXPO_PUBLIC_ENVIRONMENT=development

# Feature Flags
EXPO_PUBLIC_ENABLE_ANALYTICS=false
EXPO_PUBLIC_ENABLE_CRASH_REPORTING=false
EXPO_PUBLIC_ENABLE_PUSH_NOTIFICATIONS=false
```

### 4. Start Development Server

For web development:
```bash
npm run web
```

For mobile development:
```bash
npm start
```

This will open Expo Dev Tools where you can:
- Run on web browser
- Run on iOS simulator (macOS only)
- Run on Android emulator
- Scan QR code to run on physical device

### 5. Platform-Specific Setup

#### iOS (macOS only)
1. Install Xcode from the App Store
2. Install iOS Simulator
3. Run: `npx expo run:ios`

#### Android
1. Install Android Studio
2. Set up Android emulator or connect physical device
3. Run: `npx expo run:android`

### 6. Available Client Scripts

The following scripts are available in `package.json`:

```bash
npm start          # Start Expo development server
npm run web        # Start web version
npm run ios        # Build and run iOS
npm run android    # Build and run Android
npm run lint       # Run ESLint
```

## Features Overview

### 🔐 Authentication & Security
- **User Registration**: Email verification, strong password requirements
- **Login System**: JWT tokens, remember me, session management
- **Password Management**: Forgot password, reset functionality
- **Account Security**: Failed login tracking, account lockout
- **Session Management**: Multiple device sessions, session revocation

### 🛡️ Role-Based Access Control (RBAC)
- **Dynamic Roles**: Create custom roles with specific permissions
- **Granular Permissions**: Resource-action based permission system
- **User Assignment**: Assign/revoke roles with expiration dates
- **Permission Caching**: Efficient permission checking with Redis cache
- **Admin Interface**: Complete role and permission management

### 🎮 Gamification System
- **Achievement System**: Progressive achievements with rarity levels
- **Leaderboards**: Multiple categories (points, quests, streaks)
- **Badge Collection**: Collectible badges with special criteria
- **Reward System**: Redeemable rewards and point transactions
- **Progress Tracking**: Streak tracking and level progression

### 📚 Learning Management System
- **Course Creation**: Rich course content with lessons and chapters
- **Student Enrollment**: Course enrollment and progress tracking
- **Assignment System**: Quizzes, essays, projects with automated grading
- **Progress Analytics**: Detailed progress tracking and completion rates
- **Instructor Tools**: Course management and student analytics

### 💼 Workspace Collaboration
- **Team Workspaces**: Create and manage team collaboration spaces
- **Member Management**: Invite members with role-based permissions
- **Quest Sharing**: Share quests within workspace contexts
- **Communication**: Integrated messaging and file sharing

### 📱 Enhanced User Interface
- **Glass Theme Design**: Modern glass morphism design system
- **Responsive Layout**: Adaptive layouts for mobile, tablet, desktop
- **Interactive Animations**: Smooth micro-interactions and feedback
- **Progress Visualizations**: Animated progress indicators and charts
- **Dark/Light Modes**: Automatic theme switching support

## API Endpoints Summary

### Authentication
- `POST /auth/register` - User registration
- `POST /auth/login` - User login
- `POST /auth/refresh` - Token refresh
- `GET /auth/me` - Get user profile
- `POST /auth/logout` - User logout

### RBAC Management
- `GET /rbac/roles` - List roles
- `POST /rbac/roles` - Create role
- `POST /rbac/user-roles/assign` - Assign role to user
- `GET /rbac/user-roles/{user_id}/permissions` - Get user permissions

### Gamification
- `GET /gamification/achievements` - User achievements
- `GET /gamification/leaderboards` - Leaderboard data
- `POST /gamification/rewards/{id}/redeem` - Redeem reward
- `GET /gamification/stats` - User statistics

### Learning Management
- `GET /learning/courses` - Browse courses
- `POST /learning/enrollments` - Enroll in course
- `GET /learning/progress` - Learning progress

### Quest Management
- `GET /quests` - List quests
- `POST /quests` - Create quest
- `PUT /quests/{id}` - Update quest
- `GET /quests/stats` - Quest statistics

## Development Workflow

### 1. Daily Development
```bash
# Terminal 1: Start Redis
redis-server

# Terminal 2: Start PostgreSQL (if not running as service)
pg_ctl -D /usr/local/var/postgres start

# Terminal 3: Start backend with hot reload
cd quester/server
air

# Terminal 4: Start frontend
cd quester/client
npm start
```

### 2. Database Management

**View Tables**:
```sql
\dt
```

**Reset Database** (development only):
```sql
DROP SCHEMA public CASCADE;
CREATE SCHEMA public;
```

**Backup Database**:
```bash
pg_dump -U quester_user -d quester_db > backup.sql
```

**Restore Database**:
```bash
psql -U quester_user -d quester_db < backup.sql
```

### 3. Testing the Application

#### Backend Testing
```bash
cd quester/server
go test ./...
```

#### Frontend Testing
```bash
cd quester/client
npm test
```

#### API Testing with curl
```bash
# Health check
curl http://localhost:8080/health

# Register user
curl -X POST http://localhost:8080/api/v1/auth/register \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","username":"testuser","password":"password123","first_name":"Test","last_name":"User"}'

# Login
curl -X POST http://localhost:8080/api/v1/auth/login \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"password123"}'
```

## Production Deployment

### Environment Preparation
1. **Secure Environment Variables**: Use strong passwords and secrets
2. **Database Setup**: Configure production PostgreSQL instance
3. **Redis Setup**: Configure production Redis instance
4. **SSL Certificates**: Set up HTTPS with valid certificates
5. **Email Service**: Configure SMTP for email notifications

### Backend Deployment
```bash
# Build production binary
go build -ldflags="-s -w" -o quester-server ./cmd/server/main.go

# Create systemd service (Linux)
sudo nano /etc/systemd/system/quester.service
```

### Frontend Deployment
```bash
# Build for production
npm run build

# Deploy to hosting service (Vercel, Netlify, etc.)
```

## Troubleshooting

### Common Issues

#### Database Connection Issues
```bash
# Check PostgreSQL status
systemctl status postgresql

# Check if database exists
psql -U postgres -l

# Test connection
psql -U quester_user -d quester_db -h localhost
```

#### Redis Connection Issues
```bash
# Check Redis status
systemctl status redis

# Test Redis connection
redis-cli ping
```

#### Go Module Issues
```bash
# Clean module cache
go clean -modcache

# Re-download dependencies
go mod download
```

#### Expo/React Native Issues
```bash
# Clear Expo cache
npx expo r -c

# Clear npm cache
npm cache clean --force

# Reinstall dependencies
rm -rf node_modules package-lock.json
npm install
```

## Default Admin Access

After running the server for the first time, you can log in with:

- **Email**: `<EMAIL>`
- **Password**: `admin123`

**⚠️ Important**: Change this password immediately in production!

## Support and Documentation

- **API Documentation**: See `server/API_DOCUMENTATION.md`
- **Server Documentation**: See `server/README.md`
- **Client Documentation**: See `client/README.md`
- **Issues**: Report issues via GitHub Issues

---

**Happy Questing! 🎯**