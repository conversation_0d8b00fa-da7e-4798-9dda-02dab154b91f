package models

import (
	"time"
	"github.com/go-playground/validator/v10"
)

// UserRole represents user roles in the system
type UserRole string

const (
	RoleUser      UserRole = "user"
	RoleAdmin     UserRole = "admin"
	RoleModerator UserRole = "moderator"
)

// User represents a user in the system with enhanced authentication fields
type User struct {
	BaseModel

	// Basic Information
	Username  string `json:"username" gorm:"uniqueIndex;not null;size:50" validate:"required,min=3,max=50,alphanum"`
	Email     string `json:"email" gorm:"uniqueIndex;not null;size:255" validate:"required,email"`
	FirstName string `json:"first_name" gorm:"size:100" validate:"max=100"`
	LastName  string `json:"last_name" gorm:"size:100" validate:"max=100"`

	// Authentication
	PasswordHash string `json:"-" gorm:"not null;size:255"`

	// Profile
	Avatar      string `json:"avatar" gorm:"size:500"`
	Bio         string `json:"bio" gorm:"type:text"`
	Level       int    `json:"level" gorm:"default:1"`
	TotalPoints int    `json:"total_points" gorm:"default:0"`

	// Status
	IsVerified bool      `json:"is_verified" gorm:"default:false"`
	IsActive   bool      `json:"is_active" gorm:"default:true"`
	Role       UserRole  `json:"role" gorm:"default:'user';type:varchar(20)"`
	LastLoginAt *time.Time `json:"last_login_at"`

	// Relationships
	Quests        []Quest        `json:"quests,omitempty"`
	Tasks         []Task         `json:"tasks,omitempty"`
	Rewards       []Reward       `json:"rewards,omitempty"`
	UserRewards   []UserReward   `json:"user_rewards,omitempty"`
	Comments      []Comment      `json:"comments,omitempty"`
	Notifications []Notification `json:"notifications,omitempty"`
	Sessions      []UserSession  `json:"sessions,omitempty"`
	ActivityLogs  []ActivityLog  `json:"activity_logs,omitempty"`
	TeamMembers   []TeamMember   `json:"team_members,omitempty"`
	UserStats     *UserStats     `json:"user_stats,omitempty"`
	Preferences   *UserPreference `json:"preferences,omitempty"`
	APIKeys       []APIKey       `json:"api_keys,omitempty"`
}

// UserRequest represents the request structure for user operations
type UserRequest struct {
	Username  string `json:"username" validate:"required,min=3,max=50,alphanum"`
	Email     string `json:"email" validate:"required,email"`
	Password  string `json:"password" validate:"required,min=8,max=128"`
	FirstName string `json:"first_name" validate:"max=100"`
	LastName  string `json:"last_name" validate:"max=100"`
}

// UserUpdateRequest represents the request structure for updating user profile
type UserUpdateRequest struct {
	FirstName *string `json:"first_name" validate:"omitempty,max=100"`
	LastName  *string `json:"last_name" validate:"omitempty,max=100"`
	Bio       *string `json:"bio" validate:"omitempty,max=1000"`
	Avatar    *string `json:"avatar" validate:"omitempty,url"`
}

// ChangePasswordRequest represents the request structure for changing password
type ChangePasswordRequest struct {
	CurrentPassword string `json:"current_password" validate:"required"`
	NewPassword     string `json:"new_password" validate:"required,min=8,max=128"`
}

// UserResponse represents the response structure for user data (without sensitive info)
type UserResponse struct {
	ID          uint      `json:"id"`
	Username    string    `json:"username"`
	Email       string    `json:"email"`
	FirstName   string    `json:"first_name"`
	LastName    string    `json:"last_name"`
	Avatar      string    `json:"avatar"`
	Bio         string    `json:"bio"`
	Level       int       `json:"level"`
	TotalPoints int       `json:"total_points"`
	IsVerified  bool      `json:"is_verified"`
	IsActive    bool      `json:"is_active"`
	Role        UserRole  `json:"role"`
	LastLoginAt *time.Time `json:"last_login_at"`
	CreatedAt   time.Time `json:"created_at"`
	UpdatedAt   time.Time `json:"updated_at"`
}

// ToResponse converts a User model to UserResponse
func (u *User) ToResponse() UserResponse {
	return UserResponse{
		ID:          u.ID,
		Username:    u.Username,
		Email:       u.Email,
		FirstName:   u.FirstName,
		LastName:    u.LastName,
		Avatar:      u.Avatar,
		Bio:         u.Bio,
		Level:       u.Level,
		TotalPoints: u.TotalPoints,
		IsVerified:  u.IsVerified,
		IsActive:    u.IsActive,
		Role:        u.Role,
		LastLoginAt: u.LastLoginAt,
		CreatedAt:   u.CreatedAt,
		UpdatedAt:   u.UpdatedAt,
	}
}

// GetFullName returns the user's full name
func (u *User) GetFullName() string {
	if u.FirstName == "" && u.LastName == "" {
		return u.Username
	}
	if u.FirstName == "" {
		return u.LastName
	}
	if u.LastName == "" {
		return u.FirstName
	}
	return u.FirstName + " " + u.LastName
}

// IsAdmin checks if the user has admin role
func (u *User) IsAdmin() bool {
	return u.Role == RoleAdmin
}

// IsModerator checks if the user has moderator role
func (u *User) IsModerator() bool {
	return u.Role == RoleModerator
}

// HasRole checks if the user has a specific role
func (u *User) HasRole(role UserRole) bool {
	return u.Role == role
}

// CanModerate checks if the user can moderate (admin or moderator)
func (u *User) CanModerate() bool {
	return u.IsAdmin() || u.IsModerator()
}

// Validate validates user data
func (u *User) Validate() error {
	validate := validator.New()
	return validate.Struct(u)
}

// ValidateRequest validates user request data
func (ur *UserRequest) Validate() error {
	validate := validator.New()
	return validate.Struct(ur)
}

// ValidateUpdateRequest validates user update request data
func (uur *UserUpdateRequest) Validate() error {
	validate := validator.New()
	return validate.Struct(uur)
}

// ValidateChangePassword validates change password request data
func (cpr *ChangePasswordRequest) Validate() error {
	validate := validator.New()
	return validate.Struct(cpr)
}