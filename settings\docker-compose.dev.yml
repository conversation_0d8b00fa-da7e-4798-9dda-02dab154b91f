services:
  server:
    build:
      context: ..
      dockerfile: settings/server.dockerfile
      target: development
    container_name: quester-server-dev
    ports:
      - "8000:8000"
    volumes:
      - ../server:/app
      - /app/tmp
      - go_modules:/go/pkg/mod
    environment:
      - GO_ENV=development
      - SERVER_PORT=8000
      - DB_HOST=postgres
      - DB_PORT=5432
      - DB_USER=${DB_USER:-postgres}
      - DB_PASSWORD=${DB_PASSWORD:-password}
      - DB_NAME=${DB_NAME:-quester_dev}
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - CGO_ENABLED=0
      - CORS_ALLOW_ORIGINS=${CORS_ALLOW_ORIGINS:-http://localhost:8081,http://localhost:19000,http://localhost:3000}
      - ENABLE_SEEDING=${ENABLE_SEEDING:-false}
      - ENABLE_TEST_SEEDING=${ENABLE_TEST_SEEDING:-false}
    depends_on:
      - postgres
      - redis
    networks:
      - quester-network
    restart: unless-stopped

  client:
    build:
      context: ..
      dockerfile: settings/client.dockerfile
      target: development
    container_name: quester-client-dev
    ports:
      - "19000:19000"
      - "8081:8081"
    volumes:
      - ../client:/app
      - node_modules:/app/node_modules
      - /app/.expo
      - /app/.expo-shared
    environment:
      - NODE_ENV=development
      - EXPO_DEVTOOLS_LISTEN_ADDRESS=0.0.0.0
      - REACT_NATIVE_PACKAGER_HOSTNAME=0.0.0.0
      - EXPO_NO_TELEMETRY=1
      - API_URL=http://server:8000
      - EXPO_PUBLIC_API_URL=http://server:8000
      - EXPO_USE_FAST_RESOLVER=1
      - EXPO_NO_CACHE=1
      - WATCHMAN_DISABLE_CI=1
      - METRO_ENABLE_FAST_REFRESH=1
      - METRO_WATCH_TIMEOUT=30000
      - METRO_WATCH_POLLING=true
    networks:
      - quester-network
    restart: unless-stopped

  postgres:
    image: postgres:15-alpine
    container_name: quester-postgres-dev
    ports:
      - "5432:5432"
    environment:
      - POSTGRES_USER=${DB_USER:-postgres}
      - POSTGRES_PASSWORD=${DB_PASSWORD:-password}
      - POSTGRES_DB=${DB_NAME:-quester_dev}
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ../server/migrations:/docker-entrypoint-initdb.d
    networks:
      - quester-network
    restart: unless-stopped

  redis:
    image: redis:7-alpine
    container_name: quester-redis-dev
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - quester-network
    restart: unless-stopped

  mailhog:
    image: mailhog/mailhog:latest
    container_name: quester-mailhog-dev
    ports:
      - "8025:8025"
      - "1025:1025"
    networks:
      - quester-network
    restart: unless-stopped

volumes:
  postgres_data:
  redis_data:
  go_modules:
  node_modules:

networks:
  quester-network:
    driver: bridge
