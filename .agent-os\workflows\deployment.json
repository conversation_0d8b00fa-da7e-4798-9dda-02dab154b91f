{"name": "deployment-workflow", "description": "Production deployment workflow for Quester application", "version": "1.0.0", "environments": ["staging", "production"], "steps": [{"id": "pre-deployment-checks", "name": "Pre-deployment Checks", "description": "Validate environment and dependencies", "commands": ["docker --version", "docker-compose --version", "./env-switch.sh $TARGET_ENV", "docker-compose -f settings/docker-compose.$TARGET_ENV.yml config"], "validation": {"command": "echo 'Environment validation complete'", "expected_output": "complete"}}, {"id": "run-full-test-suite", "name": "Run Full Test Suite", "description": "Execute comprehensive tests before deployment", "depends_on": ["pre-deployment-checks"], "parallel": true, "tasks": [{"name": "Backend tests", "commands": ["cd server", "go test ./... -v -race -cover"]}, {"name": "Frontend tests", "commands": ["cd client", "npm test -- --coverage --watchAll=false"]}, {"name": "Integration tests", "commands": ["./test-integration.sh"]}]}, {"id": "build-containers", "name": "Build Production Containers", "description": "Build optimized containers for production", "depends_on": ["run-full-test-suite"], "commands": ["docker-compose -f settings/docker-compose.$TARGET_ENV.yml build --no-cache", "docker images | grep quester"]}, {"id": "database-backup", "name": "Database Backup", "description": "Create backup before deployment", "depends_on": ["build-containers"], "condition": "$TARGET_ENV == 'production'", "commands": ["./scripts/backup-database.sh $TARGET_ENV", "./scripts/verify-backup.sh"]}, {"id": "deploy-infrastructure", "name": "Deploy Infrastructure", "description": "Deploy database and supporting services", "depends_on": ["database-backup"], "commands": ["docker-compose -f settings/docker-compose.$TARGET_ENV.yml up -d postgres redis nginx", "sleep 30"], "validation": {"command": "./docker.sh $TARGET_ENV status", "expected_output": "running"}}, {"id": "run-migrations", "name": "Database Migrations", "description": "Apply database schema changes", "depends_on": ["deploy-infrastructure"], "commands": ["docker-compose -f settings/docker-compose.$TARGET_ENV.yml exec postgres pg_isready -U postgres", "docker-compose -f settings/docker-compose.$TARGET_ENV.yml run --rm server go run cmd/migrate/main.go"]}, {"id": "deploy-backend", "name": "Deploy Backend Services", "description": "Deploy Go backend application", "depends_on": ["run-migrations"], "commands": ["docker-compose -f settings/docker-compose.$TARGET_ENV.yml up -d server", "sleep 20"], "validation": {"command": "curl -f http://localhost:8000/health", "timeout": 60, "retries": 5}}, {"id": "deploy-frontend", "name": "Deploy Frontend Application", "description": "Deploy React Native web build", "depends_on": ["deploy-backend"], "commands": ["cd client && npm run build", "docker-compose -f settings/docker-compose.$TARGET_ENV.yml up -d client"], "validation": {"command": "curl -f http://localhost:19000", "timeout": 60, "retries": 3}}, {"id": "health-checks", "name": "Post-deployment Health Checks", "description": "Verify all services are running correctly", "depends_on": ["deploy-frontend"], "commands": ["./scripts/health-check.sh $TARGET_ENV", "./scripts/smoke-test.sh $TARGET_ENV"]}, {"id": "cleanup-old-images", "name": "Cleanup Old Images", "description": "Remove unused Docker images and containers", "depends_on": ["health-checks"], "commands": ["docker image prune -f", "docker container prune -f"]}], "rollback_strategy": {"trigger_conditions": ["health_check_failure", "manual_trigger"], "steps": [{"name": "Stop new deployment", "commands": ["docker-compose -f settings/docker-compose.$TARGET_ENV.yml down"]}, {"name": "Restore database", "commands": ["./scripts/restore-database.sh $TARGET_ENV"]}, {"name": "Deploy previous version", "commands": ["docker-compose -f settings/docker-compose.$TARGET_ENV.yml up -d"]}]}, "notifications": {"on_success": {"webhook": "$DEPLOYMENT_SUCCESS_WEBHOOK", "message": "Quester deployment to $TARGET_ENV completed successfully"}, "on_failure": {"webhook": "$DEPLOYMENT_FAILURE_WEBHOOK", "message": "Quester deployment to $TARGET_ENV failed at step $FAILED_STEP"}}, "environment_variables": {"TARGET_ENV": "staging", "DEPLOYMENT_TIMEOUT": "600", "HEALTH_CHECK_RETRIES": "5"}}