package models

import (
	"time"

	"gorm.io/gorm"
)

// BaseModel contains common fields for all models
type BaseModel struct {
	ID        uint           `json:"id" gorm:"primaryKey"`
	CreatedAt time.Time      `json:"created_at"`
	UpdatedAt time.Time      `json:"updated_at"`
	DeletedAt gorm.DeletedAt `json:"deleted_at,omitempty" gorm:"index"`
}

// BeforeCreate is a GORM hook that runs before creating a record
func (base *BaseModel) BeforeCreate(tx *gorm.DB) error {
	now := time.Now().UTC()
	base.CreatedAt = now
	base.UpdatedAt = now
	return nil
}

// BeforeUpdate is a GORM hook that runs before updating a record
func (base *BaseModel) BeforeUpdate(tx *gorm.DB) error {
	base.UpdatedAt = time.Now().UTC()
	return nil
}

// IsDeleted returns true if the record is soft deleted
func (base *BaseModel) IsDeleted() bool {
	return base.DeletedAt.Valid
}

// GetID returns the ID of the record
func (base *BaseModel) GetID() uint {
	return base.ID
}

// GetCreatedAt returns the creation time of the record
func (base *BaseModel) GetCreatedAt() time.Time {
	return base.CreatedAt
}

// GetUpdatedAt returns the last update time of the record
func (base *BaseModel) GetUpdatedAt() time.Time {
	return base.UpdatedAt
}

// GetDeletedAt returns the deletion time of the record (if soft deleted)
func (base *BaseModel) GetDeletedAt() *time.Time {
	if base.DeletedAt.Valid {
		return &base.DeletedAt.Time
	}
	return nil
}


// Quest represents a main goal or objective
type Quest struct {
	BaseModel
	Title       string     `json:"title" gorm:"not null"`
	Description string     `json:"description"`
	Status      string     `json:"status" gorm:"default:'draft'"` // draft, active, completed, abandoned
	Priority    string     `json:"priority" gorm:"default:'medium'"` // low, medium, high, urgent
	Difficulty  int        `json:"difficulty" gorm:"default:1"` // 1-5 scale
	Points      int        `json:"points" gorm:"default:0"`
	StartDate   *time.Time `json:"start_date"`
	DueDate     *time.Time `json:"due_date"`
	CompletedAt *time.Time `json:"completed_at"`
	IsPublic    bool       `json:"is_public" gorm:"default:false"`
	IsTemplate  bool       `json:"is_template" gorm:"default:false"`

	// Foreign keys
	UserID     uint `json:"user_id" gorm:"not null"`
	CategoryID *uint `json:"category_id"`

	// Relationships
	User        User         `json:"user"`
	Category    *Category    `json:"category,omitempty"`
	Tasks       []Task       `json:"tasks,omitempty"`
	Comments    []Comment    `json:"comments,omitempty"`
	Attachments []Attachment `json:"attachments,omitempty"`
	Tags        []Tag        `json:"tags,omitempty" gorm:"many2many:quest_tags;"`
	TeamQuests  []TeamQuest  `json:"team_quests,omitempty"`
}

// Task represents a sub-task within a quest
type Task struct {
	BaseModel
	Title       string     `json:"title" gorm:"not null"`
	Description string     `json:"description"`
	Status      string     `json:"status" gorm:"default:'pending'"` // pending, in_progress, completed, cancelled
	Priority    string     `json:"priority" gorm:"default:'medium'"` // low, medium, high, urgent
	Points      int        `json:"points" gorm:"default:0"`
	Order       int        `json:"order" gorm:"default:0"`
	DueDate     *time.Time `json:"due_date"`
	CompletedAt *time.Time `json:"completed_at"`

	// Foreign keys
	QuestID *uint `json:"quest_id"`
	UserID  uint  `json:"user_id" gorm:"not null"`

	// Relationships
	Quest    *Quest    `json:"quest,omitempty"`
	User     User      `json:"user"`
	Comments []Comment `json:"comments,omitempty"`
	Tags     []Tag     `json:"tags,omitempty" gorm:"many2many:task_tags;"`
}

// Reward represents rewards that users can earn or claim
type Reward struct {
	BaseModel
	Title       string `json:"title" gorm:"not null"`
	Description string `json:"description"`
	Type        string `json:"type" gorm:"default:'badge'"` // badge, points, item, experience
	Value       int    `json:"value" gorm:"default:0"`
	Icon        string `json:"icon"`
	Color       string `json:"color"`
	IsActive    bool   `json:"is_active" gorm:"default:true"`

	// Foreign keys
	UserID uint `json:"user_id" gorm:"not null"`

	// Relationships
	User        User         `json:"user"`
	UserRewards []UserReward `json:"user_rewards,omitempty"`
}

// UserReward represents rewards claimed by users
type UserReward struct {
	BaseModel
	ClaimedAt time.Time `json:"claimed_at"`

	// Foreign keys
	UserID   uint `json:"user_id" gorm:"not null"`
	RewardID uint `json:"reward_id" gorm:"not null"`

	// Relationships
	User   User   `json:"user"`
	Reward Reward `json:"reward"`
}

// Category represents quest/task categories
type Category struct {
	BaseModel
	Name        string `json:"name" gorm:"not null;uniqueIndex"`
	Description string `json:"description"`
	Color       string `json:"color" gorm:"default:'#6B7280'"`
	Icon        string `json:"icon"`
	IsActive    bool   `json:"is_active" gorm:"default:true"`

	// Relationships
	Quests []Quest `json:"quests,omitempty"`
}

// Tag represents labels for quests and tasks
type Tag struct {
	BaseModel
	Name        string `json:"name" gorm:"not null;uniqueIndex"`
	Color       string `json:"color" gorm:"default:'#6B7280'"`
	Description string `json:"description"`
	IsActive    bool   `json:"is_active" gorm:"default:true"`

	// Relationships
	Quests []Quest `json:"quests,omitempty" gorm:"many2many:quest_tags;"`
	Tasks  []Task  `json:"tasks,omitempty" gorm:"many2many:task_tags;"`
}

// Comment represents comments on quests and tasks
type Comment struct {
	BaseModel
	Content string `json:"content" gorm:"not null"`
	Type    string `json:"type" gorm:"default:'comment'"` // comment, note, update

	// Foreign keys
	UserID  uint  `json:"user_id" gorm:"not null"`
	QuestID *uint `json:"quest_id"`
	TaskID  *uint `json:"task_id"`

	// Relationships
	User  User   `json:"user"`
	Quest *Quest `json:"quest,omitempty"`
	Task  *Task  `json:"task,omitempty"`
}

// Attachment represents file attachments
type Attachment struct {
	BaseModel
	FileName    string `json:"file_name" gorm:"not null"`
	FilePath    string `json:"file_path" gorm:"not null"`
	FileSize    int64  `json:"file_size"`
	MimeType    string `json:"mime_type"`
	Description string `json:"description"`

	// Foreign keys
	UserID  uint  `json:"user_id" gorm:"not null"`
	QuestID *uint `json:"quest_id"`
	TaskID  *uint `json:"task_id"`

	// Relationships
	User  User   `json:"user"`
	Quest *Quest `json:"quest,omitempty"`
	Task  *Task  `json:"task,omitempty"`
}

// Notification represents user notifications
type Notification struct {
	BaseModel
	Title   string     `json:"title" gorm:"not null"`
	Message string     `json:"message" gorm:"not null"`
	Type    string     `json:"type" gorm:"default:'info'"` // info, success, warning, error
	IsRead  bool       `json:"is_read" gorm:"default:false"`
	ReadAt  *time.Time `json:"read_at"`

	// Foreign keys
	UserID uint `json:"user_id" gorm:"not null"`

	// Relationships
	User User `json:"user"`
}

// UserSession represents user login sessions
type UserSession struct {
	BaseModel
	Token     string     `json:"-" gorm:"uniqueIndex;not null"`
	ExpiresAt time.Time  `json:"expires_at"`
	IsActive  bool       `json:"is_active" gorm:"default:true"`
	IPAddress string     `json:"ip_address"`
	UserAgent string     `json:"user_agent"`
	RevokedAt *time.Time `json:"revoked_at"`

	// Foreign keys
	UserID uint `json:"user_id" gorm:"not null"`

	// Relationships
	User User `json:"user"`
}

// ActivityLog represents user activity tracking
type ActivityLog struct {
	BaseModel
	Action      string `json:"action" gorm:"not null"`
	Description string `json:"description"`
	IPAddress   string `json:"ip_address"`
	UserAgent   string `json:"user_agent"`

	// Foreign keys
	UserID uint `json:"user_id" gorm:"not null"`

	// Relationships
	User User `json:"user"`
}

// UserStats represents user statistics
type UserStats struct {
	BaseModel
	TotalQuests      int `json:"total_quests" gorm:"default:0"`
	CompletedQuests  int `json:"completed_quests" gorm:"default:0"`
	TotalTasks       int `json:"total_tasks" gorm:"default:0"`
	CompletedTasks   int `json:"completed_tasks" gorm:"default:0"`
	TotalPoints      int `json:"total_points" gorm:"default:0"`
	CurrentStreak    int `json:"current_streak" gorm:"default:0"`
	LongestStreak    int `json:"longest_streak" gorm:"default:0"`
	LastActivityAt   *time.Time `json:"last_activity_at"`

	// Foreign keys
	UserID uint `json:"user_id" gorm:"uniqueIndex;not null"`

	// Relationships
	User User `json:"user"`
}

// QuestTemplate represents reusable quest templates
type QuestTemplate struct {
	BaseModel
	Title       string `json:"title" gorm:"not null"`
	Description string `json:"description"`
	Difficulty  int    `json:"difficulty" gorm:"default:1"`
	Points      int    `json:"points" gorm:"default:0"`
	IsPublic    bool   `json:"is_public" gorm:"default:false"`
	UsageCount  int    `json:"usage_count" gorm:"default:0"`

	// Foreign keys
	UserID     uint  `json:"user_id" gorm:"not null"`
	CategoryID *uint `json:"category_id"`

	// Relationships
	User         User           `json:"user"`
	Category     *Category      `json:"category,omitempty"`
	TaskTemplates []TaskTemplate `json:"task_templates,omitempty"`
	Tags         []Tag          `json:"tags,omitempty" gorm:"many2many:quest_template_tags;"`
}

// TaskTemplate represents reusable task templates
type TaskTemplate struct {
	BaseModel
	Title       string `json:"title" gorm:"not null"`
	Description string `json:"description"`
	Points      int    `json:"points" gorm:"default:0"`
	Order       int    `json:"order" gorm:"default:0"`

	// Foreign keys
	QuestTemplateID uint `json:"quest_template_id" gorm:"not null"`

	// Relationships
	QuestTemplate QuestTemplate `json:"quest_template"`
}

// Team represents user teams/groups
type Team struct {
	BaseModel
	Name        string `json:"name" gorm:"not null"`
	Description string `json:"description"`
	IsPublic    bool   `json:"is_public" gorm:"default:false"`
	MaxMembers  int    `json:"max_members" gorm:"default:10"`

	// Foreign keys
	OwnerID uint `json:"owner_id" gorm:"not null"`

	// Relationships
	Owner       User         `json:"owner"`
	Members     []TeamMember `json:"members,omitempty"`
	TeamQuests  []TeamQuest  `json:"team_quests,omitempty"`
}

// TeamMember represents team membership
type TeamMember struct {
	BaseModel
	Role     string    `json:"role" gorm:"default:'member'"` // owner, admin, member
	JoinedAt time.Time `json:"joined_at"`

	// Foreign keys
	TeamID uint `json:"team_id" gorm:"not null"`
	UserID uint `json:"user_id" gorm:"not null"`

	// Relationships
	Team Team `json:"team"`
	User User `json:"user"`
}

// TeamQuest represents quests shared with teams
type TeamQuest struct {
	BaseModel
	// Foreign keys
	TeamID  uint `json:"team_id" gorm:"not null"`
	QuestID uint `json:"quest_id" gorm:"not null"`

	// Relationships
	Team  Team  `json:"team"`
	Quest Quest `json:"quest"`
}

// UserPreference represents user preferences and settings
type UserPreference struct {
	BaseModel
	Theme                string `json:"theme" gorm:"default:'light'"` // light, dark, auto
	Language             string `json:"language" gorm:"default:'en'"`
	Timezone             string `json:"timezone" gorm:"default:'UTC'"`
	EmailNotifications   bool   `json:"email_notifications" gorm:"default:true"`
	PushNotifications    bool   `json:"push_notifications" gorm:"default:true"`
	WeeklyDigest         bool   `json:"weekly_digest" gorm:"default:true"`
	PublicProfile        bool   `json:"public_profile" gorm:"default:false"`
	ShowCompletedQuests  bool   `json:"show_completed_quests" gorm:"default:true"`
	DefaultQuestPrivacy  bool   `json:"default_quest_privacy" gorm:"default:false"`

	// Foreign keys
	UserID uint `json:"user_id" gorm:"uniqueIndex;not null"`

	// Relationships
	User User `json:"user"`
}

// APIKey represents API keys for external integrations
type APIKey struct {
	BaseModel
	Name        string     `json:"name" gorm:"not null"`
	Key         string     `json:"-" gorm:"uniqueIndex;not null"`
	Permissions string     `json:"permissions"` // JSON array of permissions
	IsActive    bool       `json:"is_active" gorm:"default:true"`
	LastUsedAt  *time.Time `json:"last_used_at"`
	ExpiresAt   *time.Time `json:"expires_at"`

	// Foreign keys
	UserID uint `json:"user_id" gorm:"not null"`

	// Relationships
	User User `json:"user"`
}