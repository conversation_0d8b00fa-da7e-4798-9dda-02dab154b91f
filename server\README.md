# Quester Server

A Go-based REST API server for the Quester goal tracking and achievement system.

## Features

- RESTful API with comprehensive endpoint coverage
- WebSocket support for real-time updates
- GORM-based database abstraction (PostgreSQL/SQLite)
- JWT-based authentication (placeholder implementation)
- Comprehensive configuration management
- CORS enabled for frontend integration
- Graceful shutdown handling
- Modular architecture with clean separation of concerns

## Project Structure

```
server/
├── cmd/server/           # Application entry point
│   └── main.go          # Main server file with setup and configuration
├── internal/            # Internal application code
│   ├── config/          # Configuration management
│   │   └── config.go    # Environment-based configuration
│   ├── database/        # Database connection and migrations
│   │   └── database.go  # GORM setup and migration logic
│   ├── models/          # Data models
│   │   └── base.go      # Base model and all entity definitions
│   ├── handlers/        # HTTP request handlers
│   │   └── handlers.go  # API endpoint implementations (placeholder)
│   ├── middleware/      # HTTP middleware
│   │   └── middleware.go # Auth, logging, rate limiting middleware
│   ├── routes/          # Route definitions
│   │   └── routes.go    # Complete API route setup
│   └── websocket/       # WebSocket implementation
│       └── hub.go       # WebSocket hub and client management
├── .env.example         # Environment variables template
├── go.mod              # Go module definition
└── README.md           # This file
```

## Getting Started

### Prerequisites

- Go 1.21 or higher
- PostgreSQL (optional - SQLite is used by default)
- Git

### Installation

1. Clone the repository:
   ```bash
   git clone <repository-url>
   cd quester/server
   ```

2. Install dependencies:
   ```bash
   go mod download
   ```

3. Copy environment configuration:
   ```bash
   cp .env.example .env
   ```

4. Edit `.env` file with your configuration:
   ```bash
   # For development with SQLite
   DATABASE_URL=sqlite://./quester.db

   # For production with PostgreSQL
   # DATABASE_URL=postgres://username:password@localhost:5432/quester_prod?sslmode=disable
   ```

### Running the Server

1. Build the application:
   ```bash
   go build -o quester-server ./cmd/server
   ```

2. Run the server:
   ```bash
   ./quester-server
   ```

   Or run directly with Go:
   ```bash
   go run ./cmd/server
   ```

3. The server will start on `http://localhost:8080` by default.

### API Endpoints

The server provides a comprehensive REST API:

#### Health Check
- `GET /health` - Server health status

#### Authentication (Placeholder)
- `POST /api/v1/auth/register` - User registration
- `POST /api/v1/auth/login` - User login
- `POST /api/v1/auth/logout` - User logout
- `POST /api/v1/auth/refresh` - Token refresh

#### User Management
- `GET /api/v1/users/me` - Get current user
- `PUT /api/v1/users/me` - Update current user
- `DELETE /api/v1/users/me` - Delete current user

#### Quests
- `GET /api/v1/quests` - List user quests
- `POST /api/v1/quests` - Create new quest
- `GET /api/v1/quests/:id` - Get quest details
- `PUT /api/v1/quests/:id` - Update quest
- `DELETE /api/v1/quests/:id` - Delete quest

#### Tasks
- `GET /api/v1/tasks` - List user tasks
- `POST /api/v1/tasks` - Create new task
- `GET /api/v1/tasks/:id` - Get task details
- `PUT /api/v1/tasks/:id` - Update task
- `DELETE /api/v1/tasks/:id` - Delete task

#### Additional endpoints for rewards, categories, tags, teams, notifications, analytics, and admin functions are available. See routes.go for complete list.

### WebSocket

Real-time features are available via WebSocket connection at:
- `ws://localhost:8080/ws`

### Database Models

The application includes comprehensive data models:

- **User** - User accounts and profiles
- **Quest** - Main goals and objectives
- **Task** - Sub-tasks within quests
- **Reward** - Achievement rewards
- **Category** - Quest categorization
- **Tag** - Quest and task labeling
- **Team** - Collaborative groups
- **Comment** - Quest and task comments
- **Notification** - User notifications
- **ActivityLog** - User activity tracking

### Configuration

All configuration is environment-based. Key settings:

- `PORT` - Server port (default: 8080)
- `ENVIRONMENT` - Runtime environment (development/production)
- `DATABASE_URL` - Database connection string
- `JWT_SECRET` - JWT signing secret
- `REDIS_URL` - Redis connection for caching

See `.env.example` for all available configuration options.

### Development

The codebase is structured for easy development:

1. **Handlers** - All endpoint logic is in placeholder handlers ready for implementation
2. **Middleware** - Authentication, logging, and security middleware structure is in place
3. **Models** - Complete data model definitions with GORM relationships
4. **Configuration** - Comprehensive environment-based configuration system

### Next Steps for Implementation

1. Implement authentication handlers with JWT
2. Implement CRUD operations for all models
3. Add input validation and error handling
4. Implement business logic for quest/task management
5. Add real-time WebSocket features
6. Implement comprehensive testing
7. Add API documentation (OpenAPI/Swagger)
8. Set up CI/CD pipeline

### Building for Production

```bash
# Build optimized binary
go build -ldflags="-w -s" -o quester-server ./cmd/server

# Or build with specific OS/arch
GOOS=linux GOARCH=amd64 go build -o quester-server-linux ./cmd/server
```

### Docker Support

A Dockerfile can be added for containerized deployment:

```dockerfile
FROM golang:1.21-alpine AS builder
WORKDIR /app
COPY go.mod go.sum ./
RUN go mod download
COPY . .
RUN go build -o quester-server ./cmd/server

FROM alpine:latest
RUN apk --no-cache add ca-certificates
WORKDIR /root/
COPY --from=builder /app/quester-server .
EXPOSE 8080
CMD ["./quester-server"]
```

## License

[Add your license information here]