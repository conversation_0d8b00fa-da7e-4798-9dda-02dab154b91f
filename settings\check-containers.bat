@echo off
echo === Docker Container Status Check ===
cd /d "d:\quester\settings"

echo Checking Docker service...
docker --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Docker is not running
    echo Please start Docker Desktop
    pause
    exit /b 1
)
echo ✅ Docker is running

echo.
echo Current container status:
docker-compose -f docker-compose.dev.yml ps

echo.
echo Container logs (last 10 lines each):
echo --- Server logs ---
docker-compose -f docker-compose.dev.yml logs --tail=10 server

echo.
echo --- Client logs ---
docker-compose -f docker-compose.dev.yml logs --tail=10 client

echo.
echo --- Database logs ---
docker-compose -f docker-compose.dev.yml logs --tail=10 postgres

echo.
echo Choose action:
echo 1) Restart all containers
echo 2) View live logs
echo 3) Stop all containers
echo 4) Rebuild containers
set /p choice="Enter choice (1-4): "

if "%choice%"=="1" (
    echo Restarting containers...
    docker-compose -f docker-compose.dev.yml restart
) else if "%choice%"=="2" (
    echo Showing live logs... Press Ctrl+C to exit
    docker-compose -f docker-compose.dev.yml logs -f
) else if "%choice%"=="3" (
    echo Stopping containers...
    docker-compose -f docker-compose.dev.yml down
) else if "%choice%"=="4" (
    echo Rebuilding containers...
    docker-compose -f docker-compose.dev.yml down
    docker-compose -f docker-compose.dev.yml up -d --build
) else (
    echo Invalid choice
)

pause