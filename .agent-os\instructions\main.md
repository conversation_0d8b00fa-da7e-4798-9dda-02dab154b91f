# Quester Agent OS Instructions

## Project Overview

Quester is a comprehensive full-stack application with modular architecture supporting multiple business domains including task management, learning systems, and real estate management.

### Technology Stack
- **Frontend**: React Native Expo with TypeScript
- **Backend**: Go + Fiber framework with GORM ORM
- **Database**: PostgreSQL + Redis for caching
- **Infrastructure**: Docker Compose with multi-environment support
- **Real-time**: WebSocket integration for live updates

## Core Development Patterns

### API Development
- All endpoints follow `/api/v1/{module}/*` pattern
- JWT authentication with Redis sessions
- Structured error handling with JSON responses
- GORM models with proper relationships

### Frontend Development
- File-based routing with Expo Router
- Themed components for consistency
- TypeScript strict mode
- Platform-specific implementations (iOS/Android/Web)

### Database Schema
- User-centric design with RBAC (Role-Based Access Control)
- Proper foreign key relationships
- Auto-migration on server startup
- Redis for session management and caching

## Agent Capabilities

### Code Generation
- Generate React Native components with proper theming
- Create Go handlers with proper error handling
- Generate database models with GORM relationships
- Create API endpoints following project conventions

### Testing
- Generate unit tests for Go handlers
- Create integration tests for API endpoints
- Generate React Native component tests
- Database migration tests

### Deployment
- Docker container generation
- Environment-specific configurations
- Multi-environment deployment scripts
- Health checks and monitoring

## Project Structure Reference

```
quester/
├── client/                 # React Native Expo frontend
│   ├── app/               # File-based routing (Expo Router)
│   ├── components/        # Reusable UI components
│   └── src/               # Source code
├── server/                # Go backend application
│   ├── internal/          # Private application code
│   └── cmd/               # Entry points
├── settings/              # Infrastructure & deployment
│   ├── docker-compose.*.yml
│   └── docs/              # Comprehensive documentation
├── shared/                # Shared utilities
└── .agent-os/             # Agent OS configuration
```

## Environment Management

### Development Commands
```bash
./docker.sh dev up          # Start all services
./docker.sh dev down        # Stop all services
./docker.sh dev status      # Check status
./docker.sh dev logs        # View logs
```

### Environment Switching
```bash
./env-switch.sh dev         # Development
./env-switch.sh staging     # Staging
./env-switch.sh prod        # Production
```

## Module Reference

| Module | Endpoints | Tables | Description |
|--------|-----------|---------|-------------|
| Authentication | `/api/v1/auth/*` | `users` | JWT auth, registration |
| Users | `/api/v1/users/*` | `users` | Profile management |
| Quests | `/api/v1/quests/*` | `quests` | Quest management |
| Tasks | `/api/v1/tasks/*` | `tasks` | Task operations |
| Messages | `/api/v1/messages/*` | `messages` | Real-time messaging |
| Gamification | `/api/v1/gamification/*` | `achievements`, `leaderboards` | Points, badges |
| Analytics | `/api/v1/analytics/*` | `analytics` | Metrics, insights |
| Roles | `/api/v1/roles/*` | `roles`, `permissions` | RBAC system |
| Learning | `/api/v1/learning/*` | `courses`, `lessons` | LMS features |
| Classifieds | `/api/v1/classifieds/*` | `classifieds`, `properties` | Marketplace |

## Best Practices

### Code Generation
1. Always use proper TypeScript types
2. Follow existing naming conventions
3. Implement proper error handling
4. Use themed components for UI consistency
5. Follow RESTful API patterns

### Database Operations
1. Use GORM relationships properly
2. Implement proper validation
3. Use transactions for complex operations
4. Cache frequently accessed data in Redis

### Security
1. Always validate JWT tokens
2. Implement proper RBAC checks
3. Sanitize input data
4. Use HTTPS in production

### Performance
1. Implement proper caching strategies
2. Use database indexes effectively
3. Optimize React Native components
4. Use WebSockets for real-time features

## Documentation Reference

Complete implementation documentation is available in `settings/docs/`:
- `authentication.md` - JWT auth patterns
- `quest-management.md` - Quest/Task workflows
- `messaging-system.md` - WebSocket implementation
- `gamification.md` - Points/Achievement system
- `database-api.md` - GORM patterns and API design
- `ui-ux-patterns.md` - React Native components
- `deployment-infrastructure.md` - Docker and CI/CD