# Multi-stage build for Golang server application

# Base stage for dependencies
FROM golang:1.25-alpine AS base
WORKDIR /app

# Install system dependencies
RUN apk add --no-cache \
    git \
    ca-certificates \
    tzdata

# Copy go mod files
COPY server/go.mod server/go.sum ./
RUN go mod download

# Development stage
FROM base AS development
ENV CGO_ENABLED=0
ENV GOOS=linux
ENV GOARCH=amd64
WORKDIR /app

# Install air for hot reloading
RUN go install github.com/air-verse/air@latest

# Copy source code and config
COPY server/ .

EXPOSE 8000
CMD ["air", "-c", ".air.toml"]

# Build stage
FROM base AS build
ENV CGO_ENABLED=0
ENV GOOS=linux
ENV GOARCH=amd64
WORKDIR /app

COPY server/ .
RUN go build -ldflags="-w -s" -o main ./cmd/server

# Production stage
FROM alpine:latest AS production
WORKDIR /root/

# Install runtime dependencies
RUN apk --no-cache add ca-certificates tzdata curl
RUN update-ca-certificates

# Create non-root user
RUN addgroup -g 1001 -S appgroup && \
    adduser -S appuser -u 1001 -G appgroup

# Copy built binary
COPY --from=build --chown=appuser:appgroup /app/main .
COPY --from=build --chown=appuser:appgroup /app/configs ./configs/
COPY --from=build --chown=appuser:appgroup /app/migrations ./migrations/

USER appuser
EXPOSE 8000

HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
  CMD curl -f http://localhost:8000/health || exit 1

CMD ["./main"]