# Quester System Overview

## Technology Stack

### Frontend (React Native + Expo)
```typescript
// Core dependencies
"react-native": "~0.74.0"
"expo": "~51.0.0"
"@expo/router": "file-based routing"
"@reduxjs/toolkit": "state management"
"axios": "HTTP client"
"expo-secure-store": "token storage"
```

### Backend (Go + Gin)
```go
// Key modules
github.com/gin-gonic/gin         // Web framework
gorm.io/gorm                     // ORM
github.com/golang-jwt/jwt/v5     // JWT auth
github.com/gorilla/websocket     // Real-time
```

### Database
- **Primary**: PostgreSQL with GORM
- **Cache**: Redis (optional)
- **Files**: Local uploads directory

## Core Architecture Principles

### Client Architecture
1. **File-based Routing** - Expo Router handles navigation
2. **Redux State** - Centralized state with persistence
3. **Themed Components** - Dark/light mode support
4. **JWT Authentication** - Secure token-based auth

### Server Architecture
1. **Clean Architecture** - Handlers → Services → Models
2. **Middleware Stack** - Auth, CORS, logging
3. **JWT Security** - Token validation on protected routes
4. **WebSocket Hub** - Real-time messaging system

## Current Status ✅

### Working Features
- User registration and login
- JWT token management with refresh
- Redux state management with persistence
- File-based routing with auth guards
- Basic CRUD operations (users, quests, tasks)
- Real-time messaging infrastructure
- Dark/light theme switching

### Authentication Flow
```
App Start → Check Tokens → Validate → Route Decision
    ↓
Valid Token → Dashboard (/(tabs)/)
Invalid Token → Login (/(auth)/login)
```

## Critical Implementation Notes

### File Naming Convention
```
✅ Correct: themed-view.tsx, themed-text.tsx
❌ Wrong: ThemedView.tsx, ThemedText.tsx
```

### Import Patterns
```typescript
// Redux
import { useAppDispatch, useAppSelector } from '../store';
import { login, register } from '../store/slices/authSlice';

// Navigation
import { useRouter } from 'expo-router';

// Components
import ThemedView from '../components/themed-view';
```

### API Integration Pattern
```typescript
// All API calls use async thunks
export const login = createAsyncThunk(
  'auth/login',
  async (credentials: LoginRequest, { rejectWithValue }) => {
    // Implementation
  }
);
```

## Environment Configuration

### Client (.env)
```
EXPO_PUBLIC_API_URL=http://localhost:8080/api/v1
```

### Server (.env)
```
PORT=8080
DATABASE_URL=postgresql://user:pass@localhost/quester
JWT_SECRET=your-secret-key
REDIS_URL=redis://localhost:6379
```

## Quick Start Commands

### Development
```bash
# Client
cd client
npm install
npm start

# Server
cd server
go mod download
go run cmd/server/main.go
```

### Production Build
```bash
# Client
npm run build

# Server
go build -o main cmd/server/main.go
```

This overview provides the essential foundation for understanding and regenerating the Quester system.