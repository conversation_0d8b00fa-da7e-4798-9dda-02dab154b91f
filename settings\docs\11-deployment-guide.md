# Deployment Guide

## Overview

Complete deployment guide for the Quester application including client (React Native/Expo) and server (Go) components with Docker, environment configuration, and production best practices.

## Server Deployment

### Docker Configuration

#### Dockerfile (server/Dockerfile)

```dockerfile
# Multi-stage build for Go server
FROM golang:1.21-alpine AS builder

# Install git for go modules
RUN apk add --no-cache git

# Set working directory
WORKDIR /app

# Copy go mod files
COPY go.mod go.sum ./

# Download dependencies
RUN go mod download

# Copy source code
COPY . .

# Build the application
RUN CGO_ENABLED=0 GOOS=linux go build -a -installsuffix cgo -o main cmd/server/main.go

# Final stage
FROM alpine:latest

# Install ca-certificates for HTTPS
RUN apk --no-cache add ca-certificates tzdata

# Create app user
RUN addgroup -g 1001 -S app && \
    adduser -S app -u 1001 -G app

# Set working directory
WORKDIR /app

# Copy binary from builder
COPY --from=builder /app/main .

# Copy migrations if needed
COPY --from=builder /app/migrations ./migrations

# Create uploads directory
RUN mkdir -p uploads && chown -R app:app .

# Switch to app user
USER app

# Expose port
EXPOSE 8080

# Health check
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
  CMD wget --no-verbose --tries=1 --spider http://localhost:8080/health || exit 1

# Run the application
CMD ["./main"]
```

#### docker-compose.yml

```yaml
version: '3.8'

services:
  # PostgreSQL Database
  postgres:
    image: postgres:15-alpine
    container_name: quester-postgres
    environment:
      POSTGRES_DB: quester
      POSTGRES_USER: quester_user
      POSTGRES_PASSWORD: ${DB_PASSWORD}
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./migrations:/docker-entrypoint-initdb.d
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U quester_user -d quester"]
      interval: 30s
      timeout: 10s
      retries: 3
    restart: unless-stopped
    networks:
      - quester-network

  # Redis Cache
  redis:
    image: redis:7-alpine
    container_name: quester-redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3
    restart: unless-stopped
    networks:
      - quester-network

  # Go Server
  api:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: quester-api
    environment:
      PORT: 8080
      DATABASE_URL: postgresql://quester_user:${DB_PASSWORD}@postgres:5432/quester?sslmode=disable
      REDIS_URL: redis://redis:6379
      JWT_SECRET: ${JWT_SECRET}
      JWT_REFRESH_SECRET: ${JWT_REFRESH_SECRET}
      ALLOWED_ORIGINS: ${ALLOWED_ORIGINS}
      UPLOAD_PATH: /app/uploads
      MAX_FILE_SIZE: 10485760
    ports:
      - "8080:8080"
    volumes:
      - uploads_data:/app/uploads
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:8080/health"]
      interval: 30s
      timeout: 10s
      retries: 3
    restart: unless-stopped
    networks:
      - quester-network

  # Nginx Reverse Proxy
  nginx:
    image: nginx:alpine
    container_name: quester-nginx
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
      - ./ssl:/etc/nginx/ssl
      - uploads_data:/var/www/uploads:ro
    depends_on:
      - api
    restart: unless-stopped
    networks:
      - quester-network

volumes:
  postgres_data:
  redis_data:
  uploads_data:

networks:
  quester-network:
    driver: bridge
```

#### Environment Variables (.env)

```bash
# Database
DB_PASSWORD=your_secure_db_password_here

# JWT Secrets (generate with: openssl rand -base64 32)
JWT_SECRET=your_jwt_secret_key_min_32_characters_long
JWT_REFRESH_SECRET=your_refresh_secret_key_min_32_characters

# CORS
ALLOWED_ORIGINS=https://yourdomain.com,https://app.yourdomain.com

# Optional: Email configuration
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USERNAME=<EMAIL>
SMTP_PASSWORD=your-app-password

# Optional: Cloud storage
AWS_ACCESS_KEY_ID=your_aws_access_key
AWS_SECRET_ACCESS_KEY=your_aws_secret_key
AWS_REGION=us-east-1
AWS_BUCKET=your-s3-bucket-name
```

### Nginx Configuration (nginx.conf)

```nginx
events {
    worker_connections 1024;
}

http {
    upstream api {
        server api:8080;
    }

    # Rate limiting
    limit_req_zone $binary_remote_addr zone=auth:10m rate=5r/m;
    limit_req_zone $binary_remote_addr zone=api:10m rate=100r/m;

    # Gzip compression
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_proxied any;
    gzip_comp_level 6;
    gzip_types
        text/plain
        text/css
        text/xml
        text/javascript
        application/json
        application/javascript
        application/xml+rss
        application/atom+xml
        image/svg+xml;

    server {
        listen 80;
        server_name yourdomain.com www.yourdomain.com;

        # Redirect HTTP to HTTPS
        return 301 https://$server_name$request_uri;
    }

    server {
        listen 443 ssl http2;
        server_name yourdomain.com www.yourdomain.com;

        # SSL Configuration
        ssl_certificate /etc/nginx/ssl/cert.pem;
        ssl_certificate_key /etc/nginx/ssl/key.pem;
        ssl_protocols TLSv1.2 TLSv1.3;
        ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512:ECDHE-RSA-AES256-GCM-SHA384:DHE-RSA-AES256-GCM-SHA384;
        ssl_prefer_server_ciphers off;

        # Security headers
        add_header X-Frame-Options "SAMEORIGIN" always;
        add_header X-XSS-Protection "1; mode=block" always;
        add_header X-Content-Type-Options "nosniff" always;
        add_header Referrer-Policy "no-referrer-when-downgrade" always;
        add_header Content-Security-Policy "default-src 'self' http: https: data: blob: 'unsafe-inline'" always;

        # API routes
        location /api/ {
            limit_req zone=api burst=20 nodelay;
            proxy_pass http://api;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            proxy_read_timeout 300;
            proxy_connect_timeout 300;
            proxy_send_timeout 300;
        }

        # Auth routes with stricter rate limiting
        location /api/v1/auth/ {
            limit_req zone=auth burst=5 nodelay;
            proxy_pass http://api;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }

        # WebSocket support
        location /ws {
            proxy_pass http://api;
            proxy_http_version 1.1;
            proxy_set_header Upgrade $http_upgrade;
            proxy_set_header Connection "upgrade";
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            proxy_read_timeout 86400;
        }

        # File uploads and static files
        location /uploads/ {
            alias /var/www/uploads/;
            expires 30d;
            add_header Cache-Control "public, immutable";
        }

        # Health check
        location /health {
            proxy_pass http://api;
            access_log off;
        }
    }
}
```

### Production Deployment Script (deploy.sh)

```bash
#!/bin/bash

set -e

echo "🚀 Starting Quester deployment..."

# Load environment variables
if [ -f .env ]; then
    export $(cat .env | grep -v '^#' | xargs)
fi

# Backup database (if exists)
if [ "$1" != "--skip-backup" ]; then
    echo "📦 Creating database backup..."
    docker-compose exec -T postgres pg_dump -U quester_user quester > "backup-$(date +%Y%m%d-%H%M%S).sql"
fi

# Pull latest changes
echo "📥 Pulling latest changes..."
git pull origin main

# Build and deploy
echo "🔨 Building and starting services..."
docker-compose down
docker-compose build --no-cache
docker-compose up -d

# Wait for services to be healthy
echo "⏳ Waiting for services to be ready..."
timeout 300 bash -c 'until docker-compose exec api wget --spider -q http://localhost:8080/health; do sleep 5; done'

# Run database migrations
echo "🗄️ Running database migrations..."
docker-compose exec api ./main migrate

# Cleanup old images
echo "🧹 Cleaning up old Docker images..."
docker image prune -f

echo "✅ Deployment completed successfully!"
echo "🌐 API available at: https://yourdomain.com/api/v1"
echo "📊 Health check: https://yourdomain.com/health"
```

### Health Check Endpoint (server/internal/handlers/health.go)

```go
package handlers

import (
    "net/http"
    "time"
    "github.com/gin-gonic/gin"
    "gorm.io/gorm"
)

type HealthHandler struct {
    db *gorm.DB
}

func NewHealthHandler(db *gorm.DB) *HealthHandler {
    return &HealthHandler{db: db}
}

func (h *HealthHandler) HealthCheck(c *gin.Context) {
    status := "healthy"
    checks := make(map[string]interface{})

    // Database check
    sqlDB, err := h.db.DB()
    if err != nil {
        status = "unhealthy"
        checks["database"] = map[string]interface{}{
            "status": "error",
            "error":  err.Error(),
        }
    } else {
        err = sqlDB.Ping()
        if err != nil {
            status = "unhealthy"
            checks["database"] = map[string]interface{}{
                "status": "error",
                "error":  err.Error(),
            }
        } else {
            checks["database"] = map[string]interface{}{
                "status": "healthy",
            }
        }
    }

    response := gin.H{
        "status":    status,
        "timestamp": time.Now().UTC(),
        "service":   "quester-api",
        "version":   "1.0.0",
        "checks":    checks,
    }

    if status == "healthy" {
        c.JSON(http.StatusOK, response)
    } else {
        c.JSON(http.StatusServiceUnavailable, response)
    }
}
```

## Client Deployment

### Expo/React Native Deployment

#### EAS Configuration (eas.json)

```json
{
  "cli": {
    "version": ">= 3.0.0"
  },
  "build": {
    "development": {
      "developmentClient": true,
      "distribution": "internal",
      "env": {
        "EXPO_PUBLIC_API_URL": "http://localhost:8080/api/v1",
        "EXPO_PUBLIC_WS_URL": "ws://localhost:8080/ws"
      }
    },
    "preview": {
      "distribution": "internal",
      "env": {
        "EXPO_PUBLIC_API_URL": "https://api-staging.yourdomain.com/api/v1",
        "EXPO_PUBLIC_WS_URL": "wss://api-staging.yourdomain.com/ws"
      }
    },
    "production": {
      "env": {
        "EXPO_PUBLIC_API_URL": "https://api.yourdomain.com/api/v1",
        "EXPO_PUBLIC_WS_URL": "wss://api.yourdomain.com/ws"
      }
    }
  },
  "submit": {
    "production": {}
  }
}
```

#### Build and Deploy Script (client/deploy.sh)

```bash
#!/bin/bash

set -e

echo "📱 Building Quester mobile app..."

# Check if we're in the client directory
if [ ! -f "package.json" ] || [ ! -f "app.json" ]; then
    echo "❌ Error: Please run this script from the client directory"
    exit 1
fi

# Install dependencies
echo "📦 Installing dependencies..."
npm install

# Run linting and type checking
echo "🔍 Running linting and type checking..."
npm run lint
npx tsc --noEmit

# Build for production
PLATFORM=${1:-"all"}

case $PLATFORM in
    "ios")
        echo "🍎 Building for iOS..."
        eas build --platform ios --profile production
        ;;
    "android")
        echo "🤖 Building for Android..."
        eas build --platform android --profile production
        ;;
    "all")
        echo "📱 Building for both platforms..."
        eas build --platform all --profile production
        ;;
    *)
        echo "❌ Invalid platform. Use: ios, android, or all"
        exit 1
        ;;
esac

echo "✅ Build completed successfully!"
echo "📱 Check EAS dashboard for build status: https://expo.dev"
```

### Web Deployment with Vercel

#### vercel.json

```json
{
  "version": 2,
  "builds": [
    {
      "src": "package.json",
      "use": "@vercel/static-build",
      "config": {
        "distDir": "dist"
      }
    }
  ],
  "routes": [
    {
      "src": "/(.*)",
      "dest": "/index.html"
    }
  ],
  "env": {
    "EXPO_PUBLIC_API_URL": "https://api.yourdomain.com/api/v1",
    "EXPO_PUBLIC_WS_URL": "wss://api.yourdomain.com/ws"
  }
}
```

## Monitoring and Logging

### Logging Configuration (server/internal/config/logger.go)

```go
package config

import (
    "os"
    "github.com/sirupsen/logrus"
)

func SetupLogger() *logrus.Logger {
    logger := logrus.New()

    // Set log level
    level := os.Getenv("LOG_LEVEL")
    switch level {
    case "debug":
        logger.SetLevel(logrus.DebugLevel)
    case "info":
        logger.SetLevel(logrus.InfoLevel)
    case "warn":
        logger.SetLevel(logrus.WarnLevel)
    case "error":
        logger.SetLevel(logrus.ErrorLevel)
    default:
        logger.SetLevel(logrus.InfoLevel)
    }

    // Set formatter
    if os.Getenv("LOG_FORMAT") == "json" {
        logger.SetFormatter(&logrus.JSONFormatter{
            TimestampFormat: "2006-01-02T15:04:05.000Z",
        })
    } else {
        logger.SetFormatter(&logrus.TextFormatter{
            FullTimestamp:   true,
            TimestampFormat: "2006-01-02 15:04:05",
        })
    }

    return logger
}
```

### Docker Compose with Monitoring (docker-compose.monitoring.yml)

```yaml
version: '3.8'

services:
  # Add to existing services
  prometheus:
    image: prom/prometheus:latest
    container_name: quester-prometheus
    ports:
      - "9090:9090"
    volumes:
      - ./prometheus.yml:/etc/prometheus/prometheus.yml
      - prometheus_data:/prometheus
    networks:
      - quester-network

  grafana:
    image: grafana/grafana:latest
    container_name: quester-grafana
    ports:
      - "3000:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=admin
    volumes:
      - grafana_data:/var/lib/grafana
    networks:
      - quester-network

volumes:
  prometheus_data:
  grafana_data:
```

## CI/CD Pipeline

### GitHub Actions (.github/workflows/deploy.yml)

```yaml
name: Deploy Quester

on:
  push:
    branches: [main]
  pull_request:
    branches: [main]

jobs:
  test:
    runs-on: ubuntu-latest

    services:
      postgres:
        image: postgres:15
        env:
          POSTGRES_PASSWORD: postgres
          POSTGRES_DB: quester_test
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5

    steps:
    - uses: actions/checkout@v3

    - name: Set up Go
      uses: actions/setup-go@v3
      with:
        go-version: 1.21

    - name: Run tests
      run: |
        cd server
        go mod download
        go test -v ./...

    - name: Set up Node.js
      uses: actions/setup-node@v3
      with:
        node-version: '18'

    - name: Install client dependencies
      run: |
        cd client
        npm install

    - name: Run client tests
      run: |
        cd client
        npm run test
        npm run lint

  deploy:
    needs: test
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main'

    steps:
    - uses: actions/checkout@v3

    - name: Deploy to production
      uses: appleboy/ssh-action@v0.1.5
      with:
        host: ${{ secrets.HOST }}
        username: ${{ secrets.USERNAME }}
        key: ${{ secrets.SSH_KEY }}
        script: |
          cd /var/www/quester
          git pull origin main
          ./deploy.sh --skip-backup
```

## Security Checklist

### Server Security
- [ ] JWT secrets are properly generated and stored
- [ ] Database credentials are secure
- [ ] HTTPS is enforced in production
- [ ] CORS is properly configured
- [ ] Rate limiting is implemented
- [ ] Input validation is in place
- [ ] File upload restrictions are configured
- [ ] Security headers are set

### Infrastructure Security
- [ ] Server firewall is configured
- [ ] SSH keys are used instead of passwords
- [ ] Database is not publicly accessible
- [ ] Backups are encrypted and tested
- [ ] SSL certificates are valid and auto-renewing
- [ ] Log files don't contain sensitive data

## Backup and Recovery

### Database Backup Script (backup.sh)

```bash
#!/bin/bash

set -e

BACKUP_DIR="/var/backups/quester"
TIMESTAMP=$(date +%Y%m%d_%H%M%S)
BACKUP_FILE="quester_backup_${TIMESTAMP}.sql"

# Create backup directory
mkdir -p $BACKUP_DIR

# Create database backup
docker-compose exec -T postgres pg_dump -U quester_user quester > "${BACKUP_DIR}/${BACKUP_FILE}"

# Compress backup
gzip "${BACKUP_DIR}/${BACKUP_FILE}"

# Upload to cloud storage (optional)
# aws s3 cp "${BACKUP_DIR}/${BACKUP_FILE}.gz" s3://your-backup-bucket/

# Clean up old backups (keep last 7 days)
find $BACKUP_DIR -name "*.sql.gz" -mtime +7 -delete

echo "✅ Backup completed: ${BACKUP_FILE}.gz"
```

### Recovery Script (restore.sh)

```bash
#!/bin/bash

set -e

BACKUP_FILE=$1

if [ -z "$BACKUP_FILE" ]; then
    echo "Usage: $0 <backup_file.sql.gz>"
    exit 1
fi

echo "⚠️  This will restore database from: $BACKUP_FILE"
read -p "Are you sure? (y/N): " -n 1 -r
echo

if [[ $REPLY =~ ^[Yy]$ ]]; then
    # Stop API to prevent connections
    docker-compose stop api

    # Restore database
    gunzip -c "$BACKUP_FILE" | docker-compose exec -T postgres psql -U quester_user -d quester

    # Start API
    docker-compose start api

    echo "✅ Database restored successfully"
else
    echo "❌ Restore cancelled"
fi
```

## Troubleshooting

### Common Issues

1. **Database Connection Issues**
   ```bash
   # Check database logs
   docker-compose logs postgres

   # Test connection
   docker-compose exec postgres psql -U quester_user -d quester
   ```

2. **API Not Responding**
   ```bash
   # Check API logs
   docker-compose logs api

   # Check health endpoint
   curl http://localhost:8080/health
   ```

3. **File Upload Issues**
   ```bash
   # Check upload directory permissions
   docker-compose exec api ls -la uploads/

   # Check disk space
   df -h
   ```

4. **WebSocket Connection Issues**
   ```bash
   # Test WebSocket connection
   wscat -c ws://localhost:8080/ws?user_id=1
   ```

### Performance Monitoring

```bash
# Monitor resource usage
docker stats

# Check database performance
docker-compose exec postgres psql -U quester_user -d quester -c "
SELECT schemaname,tablename,attname,n_distinct,correlation
FROM pg_stats
WHERE schemaname = 'public';"

# Monitor API response times
curl -w "@curl-format.txt" -o /dev/null -s http://localhost:8080/api/v1/health
```

This deployment guide provides a production-ready setup with proper security, monitoring, and backup procedures for the Quester application.