# WebSocket & Real-Time Implementation

## Overview

Complete implementation guide for WebSocket-based real-time communication in the Quester application, including messaging, notifications, and live updates.

## Server Implementation

### WebSocket Hub (server/internal/websocket/hub.go)

```go
package websocket

import (
    "sync"
    "log"
    "time"
    "encoding/json"
)

type Hub struct {
    // Registered clients
    clients map[*Client]bool

    // User to clients mapping
    userClients map[uint][]*Client

    // Room-based connections
    roomClients map[string][]*Client

    // Inbound messages from clients
    broadcast chan []byte

    // Register requests from clients
    register chan *Client

    // Unregister requests from clients
    unregister chan *Client

    // Mutex for thread safety
    mutex sync.RWMutex
}

func NewHub() *Hub {
    return &Hub{
        clients:     make(map[*Client]bool),
        userClients: make(map[uint][]*Client),
        roomClients: make(map[string][]*Client),
        broadcast:   make(chan []byte),
        register:    make(chan *Client),
        unregister:  make(chan *Client),
    }
}

func (h *Hub) Run() {
    for {
        select {
        case client := <-h.register:
            h.registerClient(client)

        case client := <-h.unregister:
            h.unregisterClient(client)

        case message := <-h.broadcast:
            h.broadcastMessage(message)
        }
    }
}

func (h *Hub) registerClient(client *Client) {
    h.mutex.Lock()
    defer h.mutex.Unlock()

    h.clients[client] = true

    // Add to user mapping
    if client.userID > 0 {
        h.userClients[client.userID] = append(h.userClients[client.userID], client)
    }

    log.Printf("Client connected: %s (User: %d)", client.id, client.userID)

    // Send welcome message
    welcome := Message{
        Type:      "connected",
        Data:      map[string]interface{}{"status": "connected"},
        Timestamp: time.Now(),
    }
    client.Send(welcome)
}

func (h *Hub) unregisterClient(client *Client) {
    h.mutex.Lock()
    defer h.mutex.Unlock()

    if _, ok := h.clients[client]; ok {
        delete(h.clients, client)
        close(client.send)

        // Remove from user mapping
        if client.userID > 0 {
            clients := h.userClients[client.userID]
            for i, c := range clients {
                if c == client {
                    h.userClients[client.userID] = append(clients[:i], clients[i+1:]...)
                    break
                }
            }
            if len(h.userClients[client.userID]) == 0 {
                delete(h.userClients, client.userID)
            }
        }

        log.Printf("Client disconnected: %s", client.id)
    }
}

func (h *Hub) BroadcastToUser(userID uint, message Message) {
    h.mutex.RLock()
    defer h.mutex.RUnlock()

    if clients, exists := h.userClients[userID]; exists {
        for _, client := range clients {
            client.Send(message)
        }
    }
}

func (h *Hub) BroadcastToRoom(room string, message Message) {
    h.mutex.RLock()
    defer h.mutex.RUnlock()

    if clients, exists := h.roomClients[room]; exists {
        for _, client := range clients {
            client.Send(message)
        }
    }
}
```

### WebSocket Client (server/internal/websocket/client.go)

```go
package websocket

import (
    "time"
    "log"
    "encoding/json"
    "github.com/gorilla/websocket"
)

const (
    writeWait = 10 * time.Second
    pongWait = 60 * time.Second
    pingPeriod = (pongWait * 9) / 10
    maxMessageSize = 512
)

type Client struct {
    hub    *Hub
    conn   *websocket.Conn
    send   chan []byte
    userID uint
    id     string
    isAuthenticated bool
    lastActivity time.Time
}

type Message struct {
    Type      string      `json:"type"`
    Data      interface{} `json:"data"`
    UserID    uint        `json:"user_id,omitempty"`
    Room      string      `json:"room,omitempty"`
    Timestamp time.Time   `json:"timestamp"`
}

func NewClient(hub *Hub, conn *websocket.Conn, userID uint, clientID string) *Client {
    return &Client{
        hub:    hub,
        conn:   conn,
        send:   make(chan []byte, 256),
        userID: userID,
        id:     clientID,
        isAuthenticated: userID > 0,
        lastActivity: time.Now(),
    }
}

func (c *Client) ReadPump() {
    defer func() {
        c.hub.unregister <- c
        c.conn.Close()
    }()

    c.conn.SetReadLimit(maxMessageSize)
    c.conn.SetReadDeadline(time.Now().Add(pongWait))
    c.conn.SetPongHandler(func(string) error {
        c.conn.SetReadDeadline(time.Now().Add(pongWait))
        c.lastActivity = time.Now()
        return nil
    })

    for {
        _, messageBytes, err := c.conn.ReadMessage()
        if err != nil {
            if websocket.IsUnexpectedCloseError(err, websocket.CloseGoingAway, websocket.CloseAbnormalClosure) {
                log.Printf("WebSocket error: %v", err)
            }
            break
        }

        c.lastActivity = time.Now()

        var message Message
        if err := json.Unmarshal(messageBytes, &message); err != nil {
            log.Printf("Invalid message format: %v", err)
            continue
        }

        c.handleMessage(message)
    }
}

func (c *Client) WritePump() {
    ticker := time.NewTicker(pingPeriod)
    defer func() {
        ticker.Stop()
        c.conn.Close()
    }()

    for {
        select {
        case message, ok := <-c.send:
            c.conn.SetWriteDeadline(time.Now().Add(writeWait))
            if !ok {
                c.conn.WriteMessage(websocket.CloseMessage, []byte{})
                return
            }

            w, err := c.conn.NextWriter(websocket.TextMessage)
            if err != nil {
                return
            }
            w.Write(message)

            // Add queued messages to the current message
            n := len(c.send)
            for i := 0; i < n; i++ {
                w.Write([]byte{'\n'})
                w.Write(<-c.send)
            }

            if err := w.Close(); err != nil {
                return
            }

        case <-ticker.C:
            c.conn.SetWriteDeadline(time.Now().Add(writeWait))
            if err := c.conn.WriteMessage(websocket.PingMessage, nil); err != nil {
                return
            }
        }
    }
}

func (c *Client) Send(message Message) {
    message.Timestamp = time.Now()
    data, err := json.Marshal(message)
    if err != nil {
        log.Printf("Error marshaling message: %v", err)
        return
    }

    select {
    case c.send <- data:
    default:
        close(c.send)
        delete(c.hub.clients, c)
    }
}

func (c *Client) handleMessage(message Message) {
    switch message.Type {
    case "ping":
        c.Send(Message{Type: "pong", Data: "pong"})

    case "chat_message":
        // Handle chat message
        c.handleChatMessage(message)

    case "typing":
        // Handle typing indicator
        c.handleTypingIndicator(message)

    case "status_change":
        // Handle status update
        c.handleStatusChange(message)

    case "join_room":
        // Handle room joining
        c.handleJoinRoom(message)

    case "leave_room":
        // Handle room leaving
        c.handleLeaveRoom(message)
    }
}
```

### Message Handler (server/internal/handlers/websocket.go)

```go
package handlers

import (
    "net/http"
    "strconv"
    "github.com/gin-gonic/gin"
    "github.com/gorilla/websocket"
    "github.com/google/uuid"
    "quester-server/internal/websocket"
)

var upgrader = websocket.Upgrader{
    ReadBufferSize:  1024,
    WriteBufferSize: 1024,
    CheckOrigin: func(r *http.Request) bool {
        return true // Allow all origins in development
    },
}

type WebSocketHandler struct {
    hub *websocket.Hub
}

func NewWebSocketHandler(hub *websocket.Hub) *WebSocketHandler {
    return &WebSocketHandler{hub: hub}
}

func (h *WebSocketHandler) HandleWebSocket(c *gin.Context) {
    conn, err := upgrader.Upgrade(c.Writer, c.Request, nil)
    if err != nil {
        c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to upgrade connection"})
        return
    }

    // Get user ID from query or JWT
    userIDStr := c.Query("user_id")
    var userID uint = 0
    if userIDStr != "" {
        if uid, err := strconv.ParseUint(userIDStr, 10, 32); err == nil {
            userID = uint(uid)
        }
    }

    // Generate client ID
    clientID := c.Query("client_id")
    if clientID == "" {
        clientID = uuid.New().String()
    }

    // Create client
    client := websocket.NewClient(h.hub, conn, userID, clientID)

    // Register client
    h.hub.Register <- client

    // Start goroutines
    go client.WritePump()
    go client.ReadPump()
}
```

## Client Implementation

### WebSocket Service (client/services/websocket/websocketService.ts)

```typescript
interface Message {
  type: string;
  data: any;
  user_id?: number;
  room?: string;
  timestamp: string;
}

interface WebSocketServiceConfig {
  url: string;
  reconnectAttempts: number;
  reconnectInterval: number;
  heartbeatInterval: number;
}

class WebSocketService {
  private ws: WebSocket | null = null;
  private config: WebSocketServiceConfig;
  private userId: number | null = null;
  private accessToken: string | null = null;
  private reconnectCount = 0;
  private heartbeatTimer: NodeJS.Timeout | null = null;
  private isConnecting = false;
  private messageHandlers = new Map<string, ((data: any) => void)[]>();

  constructor(config: WebSocketServiceConfig) {
    this.config = config;
  }

  async connect(userId?: number, accessToken?: string): Promise<void> {
    if (this.isConnecting || this.ws?.readyState === WebSocket.OPEN) {
      return;
    }

    this.isConnecting = true;
    this.userId = userId ?? this.userId;
    this.accessToken = accessToken ?? this.accessToken;

    try {
      const wsUrl = this.buildWebSocketUrl();
      this.ws = new WebSocket(wsUrl);

      this.ws.onopen = this.handleOpen.bind(this);
      this.ws.onmessage = this.handleMessage.bind(this);
      this.ws.onclose = this.handleClose.bind(this);
      this.ws.onerror = this.handleError.bind(this);

    } catch (error) {
      console.error('WebSocket connection failed:', error);
      this.isConnecting = false;
      throw error;
    }
  }

  disconnect(): void {
    if (this.heartbeatTimer) {
      clearInterval(this.heartbeatTimer);
      this.heartbeatTimer = null;
    }

    if (this.ws) {
      this.ws.close();
      this.ws = null;
    }

    this.isConnecting = false;
    this.reconnectCount = 0;
  }

  send(type: string, data: any, room?: string): void {
    if (!this.ws || this.ws.readyState !== WebSocket.OPEN) {
      console.warn('WebSocket not connected, cannot send message');
      return;
    }

    const message: Message = {
      type,
      data,
      user_id: this.userId || undefined,
      room,
      timestamp: new Date().toISOString(),
    };

    this.ws.send(JSON.stringify(message));
  }

  // Message subscription methods
  on(type: string, handler: (data: any) => void): void {
    if (!this.messageHandlers.has(type)) {
      this.messageHandlers.set(type, []);
    }
    this.messageHandlers.get(type)!.push(handler);
  }

  off(type: string, handler: (data: any) => void): void {
    const handlers = this.messageHandlers.get(type);
    if (handlers) {
      const index = handlers.indexOf(handler);
      if (index > -1) {
        handlers.splice(index, 1);
      }
    }
  }

  // Specific message methods
  sendChatMessage(content: string, receiverId?: number, room?: string): void {
    this.send('chat_message', {
      content,
      receiver_id: receiverId,
      room,
    });
  }

  sendTypingIndicator(isTyping: boolean, room?: string): void {
    this.send('typing', { is_typing: isTyping }, room);
  }

  updateStatus(status: 'online' | 'away' | 'busy' | 'offline'): void {
    this.send('status_change', { status });
  }

  joinRoom(room: string): void {
    this.send('join_room', { room });
  }

  leaveRoom(room: string): void {
    this.send('leave_room', { room });
  }

  private buildWebSocketUrl(): string {
    const params = new URLSearchParams();
    if (this.userId) {
      params.append('user_id', this.userId.toString());
    }
    if (this.accessToken) {
      params.append('token', this.accessToken);
    }

    return `${this.config.url}?${params.toString()}`;
  }

  private handleOpen(): void {
    console.log('WebSocket connected');
    this.isConnecting = false;
    this.reconnectCount = 0;

    // Start heartbeat
    this.startHeartbeat();

    // Emit connection event
    this.emit('connected', { userId: this.userId });
  }

  private handleMessage(event: MessageEvent): void {
    try {
      const message: Message = JSON.parse(event.data);
      this.emit(message.type, message.data);
    } catch (error) {
      console.error('Failed to parse WebSocket message:', error);
    }
  }

  private handleClose(): void {
    console.log('WebSocket disconnected');
    this.isConnecting = false;

    if (this.heartbeatTimer) {
      clearInterval(this.heartbeatTimer);
      this.heartbeatTimer = null;
    }

    // Attempt reconnection
    this.reconnect();
  }

  private handleError(error: Event): void {
    console.error('WebSocket error:', error);
    this.isConnecting = false;
  }

  private async reconnect(): Promise<void> {
    if (this.reconnectCount >= this.config.reconnectAttempts) {
      console.error('Max reconnection attempts reached');
      return;
    }

    this.reconnectCount++;
    console.log(`Reconnecting... Attempt ${this.reconnectCount}`);

    setTimeout(() => {
      this.connect();
    }, this.config.reconnectInterval);
  }

  private startHeartbeat(): void {
    this.heartbeatTimer = setInterval(() => {
      this.send('ping', 'ping');
    }, this.config.heartbeatInterval);
  }

  private emit(type: string, data: any): void {
    const handlers = this.messageHandlers.get(type);
    if (handlers) {
      handlers.forEach(handler => handler(data));
    }
  }
}

// Create singleton instance
export const websocketService = new WebSocketService({
  url: process.env.EXPO_PUBLIC_WS_URL || 'ws://localhost:8080/ws',
  reconnectAttempts: 5,
  reconnectInterval: 3000,
  heartbeatInterval: 30000,
});
```

### Real-Time Messaging Hook (client/hooks/useWebSocket.ts)

```typescript
import { useEffect, useCallback } from 'react';
import { useAppDispatch, useAppSelector } from '../store';
import { websocketService } from '../services/websocket/websocketService';
import { addMessage, updateTypingStatus } from '../store/slices/messageSlice';
import { selectUser } from '../store/slices/authSlice';

export const useWebSocket = () => {
  const dispatch = useAppDispatch();
  const user = useAppSelector(selectUser);

  useEffect(() => {
    if (!user) return;

    // Connect to WebSocket
    websocketService.connect(user.id);

    // Set up message handlers
    const handleChatMessage = (data: any) => {
      dispatch(addMessage(data));
    };

    const handleTyping = (data: any) => {
      dispatch(updateTypingStatus(data));
    };

    const handleUserOnline = (data: any) => {
      console.log('User came online:', data);
    };

    const handleUserOffline = (data: any) => {
      console.log('User went offline:', data);
    };

    // Subscribe to events
    websocketService.on('chat_message', handleChatMessage);
    websocketService.on('user_typing', handleTyping);
    websocketService.on('user_online', handleUserOnline);
    websocketService.on('user_offline', handleUserOffline);

    // Cleanup
    return () => {
      websocketService.off('chat_message', handleChatMessage);
      websocketService.off('user_typing', handleTyping);
      websocketService.off('user_online', handleUserOnline);
      websocketService.off('user_offline', handleUserOffline);
      websocketService.disconnect();
    };
  }, [user, dispatch]);

  const sendMessage = useCallback((content: string, receiverId?: number) => {
    websocketService.sendChatMessage(content, receiverId);
  }, []);

  const sendTyping = useCallback((isTyping: boolean) => {
    websocketService.sendTypingIndicator(isTyping);
  }, []);

  const updateStatus = useCallback((status: 'online' | 'away' | 'busy' | 'offline') => {
    websocketService.updateStatus(status);
  }, []);

  return {
    sendMessage,
    sendTyping,
    updateStatus,
    isConnected: websocketService.isConnected,
  };
};
```

### Real-Time Components

#### Chat Message Component (client/components/messaging/ChatMessage.tsx)

```typescript
import React from 'react';
import { View, StyleSheet } from 'react-native';
import ThemedText from '../ui/themed-text';
import ThemedView from '../ui/themed-view';

interface ChatMessageProps {
  message: {
    id: number;
    content: string;
    sender: {
      id: number;
      username: string;
      avatar?: string;
    };
    timestamp: string;
    isOwn: boolean;
  };
}

export default function ChatMessage({ message }: ChatMessageProps) {
  return (
    <View style={[
      styles.container,
      message.isOwn ? styles.ownMessage : styles.otherMessage
    ]}>
      <ThemedView style={[
        styles.bubble,
        message.isOwn ? styles.ownBubble : styles.otherBubble
      ]}>
        {!message.isOwn && (
          <ThemedText variant="caption" color="secondary" style={styles.sender}>
            {message.sender.username}
          </ThemedText>
        )}
        <ThemedText variant="body">{message.content}</ThemedText>
        <ThemedText variant="caption" color="tertiary" style={styles.timestamp}>
          {new Date(message.timestamp).toLocaleTimeString()}
        </ThemedText>
      </ThemedView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    marginVertical: 4,
    paddingHorizontal: 16,
  },
  ownMessage: {
    alignItems: 'flex-end',
  },
  otherMessage: {
    alignItems: 'flex-start',
  },
  bubble: {
    maxWidth: '80%',
    padding: 12,
    borderRadius: 16,
  },
  ownBubble: {
    backgroundColor: '#007AFF',
  },
  otherBubble: {
    backgroundColor: '#E9ECEF',
  },
  sender: {
    marginBottom: 4,
  },
  timestamp: {
    marginTop: 4,
    fontSize: 11,
  },
});
```

#### Typing Indicator (client/components/messaging/TypingIndicator.tsx)

```typescript
import React, { useEffect, useState } from 'react';
import { View, StyleSheet } from 'react-native';
import { useAppSelector } from '../../store';
import ThemedText from '../ui/themed-text';

export default function TypingIndicator() {
  const typingUsers = useAppSelector(state => state.messages.typingUsers);
  const [dots, setDots] = useState('');

  useEffect(() => {
    if (typingUsers.length === 0) return;

    const interval = setInterval(() => {
      setDots(prev => {
        if (prev === '...') return '';
        return prev + '.';
      });
    }, 500);

    return () => clearInterval(interval);
  }, [typingUsers]);

  if (typingUsers.length === 0) return null;

  const typingText = typingUsers.length === 1
    ? `${typingUsers[0].username} is typing${dots}`
    : `${typingUsers.length} people are typing${dots}`;

  return (
    <View style={styles.container}>
      <ThemedText variant="caption" color="secondary" style={styles.text}>
        {typingText}
      </ThemedText>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    paddingHorizontal: 16,
    paddingVertical: 8,
  },
  text: {
    fontStyle: 'italic',
  },
});
```

## Message Types and Handlers

### Standard Message Types

```typescript
export enum WebSocketMessageType {
  // Connection
  CONNECTED = 'connected',
  PING = 'ping',
  PONG = 'pong',

  // Chat
  CHAT_MESSAGE = 'chat_message',
  MESSAGE_RECEIVED = 'message_received',
  TYPING = 'typing',
  USER_TYPING = 'user_typing',

  // Presence
  STATUS_CHANGE = 'status_change',
  USER_STATUS_CHANGED = 'user_status_changed',
  USER_ONLINE = 'user_online',
  USER_OFFLINE = 'user_offline',

  // Rooms
  JOIN_ROOM = 'join_room',
  LEAVE_ROOM = 'leave_room',
  ROOM_JOINED = 'room_joined',
  ROOM_LEFT = 'room_left',

  // Notifications
  NOTIFICATION = 'notification',
  QUEST_UPDATE = 'quest_update',
  TASK_UPDATE = 'task_update',

  // Errors
  ERROR = 'error',
}
```

## Integration with Redux

### Message Slice (client/store/slices/messageSlice.ts)

```typescript
import { createSlice, PayloadAction } from '@reduxjs/toolkit';

interface Message {
  id: number;
  content: string;
  senderId: number;
  receiverId?: number;
  room?: string;
  timestamp: string;
}

interface TypingUser {
  id: number;
  username: string;
}

interface MessageState {
  messages: Message[];
  typingUsers: TypingUser[];
  onlineUsers: number[];
  rooms: string[];
  isConnected: boolean;
}

const initialState: MessageState = {
  messages: [],
  typingUsers: [],
  onlineUsers: [],
  rooms: [],
  isConnected: false,
};

const messageSlice = createSlice({
  name: 'messages',
  initialState,
  reducers: {
    addMessage: (state, action: PayloadAction<Message>) => {
      state.messages.push(action.payload);
    },

    updateTypingStatus: (state, action: PayloadAction<{ user: TypingUser; isTyping: boolean }>) => {
      const { user, isTyping } = action.payload;

      if (isTyping) {
        if (!state.typingUsers.find(u => u.id === user.id)) {
          state.typingUsers.push(user);
        }
      } else {
        state.typingUsers = state.typingUsers.filter(u => u.id !== user.id);
      }
    },

    setConnectionStatus: (state, action: PayloadAction<boolean>) => {
      state.isConnected = action.payload;
    },

    addOnlineUser: (state, action: PayloadAction<number>) => {
      if (!state.onlineUsers.includes(action.payload)) {
        state.onlineUsers.push(action.payload);
      }
    },

    removeOnlineUser: (state, action: PayloadAction<number>) => {
      state.onlineUsers = state.onlineUsers.filter(id => id !== action.payload);
    },
  },
});

export const {
  addMessage,
  updateTypingStatus,
  setConnectionStatus,
  addOnlineUser,
  removeOnlineUser,
} = messageSlice.actions;

export default messageSlice.reducer;
```

## Testing WebSocket Implementation

### Server Testing

```bash
# Test WebSocket connection
wscat -c ws://localhost:8080/ws?user_id=1

# Send test message
{"type":"ping","data":"test"}

# Expected response
{"type":"pong","data":"pong","timestamp":"2024-01-20T10:00:00Z"}
```

### Client Testing

```typescript
// Test WebSocket service
import { websocketService } from './services/websocket/websocketService';

// Connect
await websocketService.connect(1, 'your-jwt-token');

// Send message
websocketService.sendChatMessage('Hello, World!');

// Listen for messages
websocketService.on('chat_message', (data) => {
  console.log('Received message:', data);
});
```

## Performance Considerations

### Server Optimizations

1. **Connection Pooling**: Limit concurrent connections per user
2. **Message Buffering**: Batch messages for efficiency
3. **Heartbeat Management**: Detect and clean up dead connections
4. **Memory Management**: Efficient client mapping and cleanup

### Client Optimizations

1. **Automatic Reconnection**: Handle connection drops gracefully
2. **Message Queuing**: Queue messages when offline
3. **Event Debouncing**: Limit typing indicator frequency
4. **Memory Cleanup**: Properly unsubscribe from events

## Security Considerations

1. **Authentication**: Validate JWT tokens on connection
2. **Rate Limiting**: Prevent message spam
3. **Input Validation**: Sanitize all incoming messages
4. **CORS Configuration**: Restrict allowed origins
5. **Message Encryption**: Optional end-to-end encryption

This implementation provides a complete real-time communication system with proper error handling, reconnection logic, and integration with the existing Redux state management.