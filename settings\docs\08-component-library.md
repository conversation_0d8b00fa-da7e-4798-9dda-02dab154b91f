# Component Library

This document provides comprehensive documentation for the reusable UI components available in the Quester codebase. These components are built using React Native Reusables, class-variance-authority (cva) for styling variants, and are designed for consistency, reusability, and rapid development across the client application.

## Overview

The component library consists of:
- **Form Components**: Pre-built forms for authentication and user management
- **UI Components**: Atomic design system components for building interfaces
- **Layout Components**: Higher-level layout and navigation components
- **Common Components**: Utility components for common UI patterns

All components support:
- Light/dark theme integration
- Platform-specific optimizations (iOS/Android/Web)
- Accessibility features
- TypeScript definitions
- Tailwind CSS className support via NativeWind

## Form Components

### SignInForm (`sign-in-form.tsx`)

A complete sign-in form with email/password fields, social connections, and navigation to other auth flows.

**Features:**
- Email and password input fields
- Social login integration
- Forgot password link
- Sign up navigation
- Form validation
- Keyboard navigation support

**Usage:**
```tsx
import { SignInForm } from '@/components/sign-in-form';

export default function LoginScreen() {
  return (
    <View className="flex-1 justify-center p-6">
      <SignInForm />
    </View>
  );
}
```

**Props:** None (self-contained component)

### SignUpForm (`sign-up-form.tsx`)

User registration form with validation and account creation flow.

**Features:**
- Email, password, and confirm password fields
- Terms acceptance checkbox
- Social registration options
- Form validation with error handling
- Navigation to sign-in

### ForgotPasswordForm (`forgot-password-form.tsx`)

Password reset request form.

**Features:**
- Email input for reset request
- Validation and error handling
- Success state with instructions
- Navigation back to sign-in

### ResetPasswordForm (`reset-password-form.tsx`)

Password reset completion form with token validation.

**Features:**
- New password and confirmation fields
- Token validation from URL/reset link
- Password strength requirements
- Success confirmation

### VerifyEmailForm (`verify-email-form.tsx`)

Email verification form for new user accounts.

**Features:**
- Verification code input
- Resend verification email
- Token validation
- Success/error states

### SocialConnections (`social-connections.tsx`)

Social login buttons and integration.

**Features:**
- Multiple provider support (Google, Apple, etc.)
- OAuth flow handling
- Loading states
- Error handling

### UserMenu (`user-menu.tsx`)

User account dropdown menu with profile options.

**Features:**
- User avatar and name display
- Account settings navigation
- Logout functionality
- Profile management links

## UI Components (Atomic)

### Accordion (`ui/accordion.tsx`)

Collapsible content sections with smooth animations.

**Variants:**
- Single expandable section
- Multiple expandable sections
- Custom trigger styling

**Usage:**
```tsx
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from '@/components/ui/accordion';

<Accordion type="single" collapsible>
  <AccordionItem value="item-1">
    <AccordionTrigger>Section 1</AccordionTrigger>
    <AccordionContent>Content for section 1</AccordionContent>
  </AccordionItem>
</Accordion>
```

### Alert (`ui/alert.tsx`)

Informational messages and notifications.

**Variants:**
- `default`, `destructive`

**Usage:**
```tsx
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';

<Alert>
  <AlertTitle>Success!</AlertTitle>
  <AlertDescription>Your changes have been saved.</AlertDescription>
</Alert>
```

### AlertDialog (`ui/alert-dialog.tsx`)

Modal confirmation dialogs with actions.

**Usage:**
```tsx
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle, AlertDialogTrigger } from '@/components/ui/alert-dialog';

<AlertDialog>
  <AlertDialogTrigger>
    <Button variant="destructive">Delete</Button>
  </AlertDialogTrigger>
  <AlertDialogContent>
    <AlertDialogHeader>
      <AlertDialogTitle>Are you sure?</AlertDialogTitle>
      <AlertDialogDescription>This action cannot be undone.</AlertDialogDescription>
    </AlertDialogHeader>
    <AlertDialogFooter>
      <AlertDialogCancel>Cancel</AlertDialogCancel>
      <AlertDialogAction>Delete</AlertDialogAction>
    </AlertDialogFooter>
  </AlertDialogContent>
</AlertDialog>
```

### AspectRatio (`ui/aspect-ratio.tsx`)

Maintains aspect ratio for responsive content.

**Usage:**
```tsx
import { AspectRatio } from '@/components/ui/aspect-ratio';

<AspectRatio ratio={16 / 9}>
  <Image source={{ uri: '...' }} className="w-full h-full" />
</AspectRatio>
```

### Avatar (`ui/avatar.tsx`)

User profile image display with fallbacks.

**Usage:**
```tsx
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';

<Avatar>
  <AvatarImage source={{ uri: user.avatarUrl }} />
  <AvatarFallback>{user.initials}</AvatarFallback>
</Avatar>
```

### Badge (`ui/badge.tsx`)

Status indicators and labels.

**Variants:**
- `default`, `secondary`, `destructive`, `outline`

**Usage:**
```tsx
import { Badge } from '@/components/ui/badge';

<Badge variant="secondary">New</Badge>
```

### Button (`ui/button.tsx`)

Interactive button component with multiple variants and sizes.

**Variants:**
- `default`, `destructive`, `outline`, `secondary`, `ghost`, `link`

**Sizes:**
- `default`, `sm`, `lg`, `icon`

**Props:**
```tsx
interface ButtonProps {
  variant?: 'default' | 'destructive' | 'outline' | 'secondary' | 'ghost' | 'link';
  size?: 'default' | 'sm' | 'lg' | 'icon';
  disabled?: boolean;
  children: React.ReactNode;
}
```

**Usage:**
```tsx
import { Button } from '@/components/ui/button';

<Button variant="default" size="default" onPress={handlePress}>
  Click me
</Button>
```

### Card (`ui/card.tsx`)

Container component for grouped content.

**Components:**
- `Card`: Main container
- `CardHeader`: Header section
- `CardTitle`: Title text
- `CardDescription`: Description text
- `CardContent`: Main content area
- `CardFooter`: Footer actions

**Usage:**
```tsx
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';

<Card>
  <CardHeader>
    <CardTitle>Card Title</CardTitle>
    <CardDescription>Card description</CardDescription>
  </CardHeader>
  <CardContent>
    <Text>Card content</Text>
  </CardContent>
</Card>
```

### Checkbox (`ui/checkbox.tsx`)

Boolean input control with custom styling.

**Usage:**
```tsx
import { Checkbox } from '@/components/ui/checkbox';

<Checkbox checked={isChecked} onCheckedChange={setIsChecked} />
```

### Collapsible (`ui/collapsible.tsx`)

Collapsible content with animation.

**Usage:**
```tsx
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@/components/ui/collapsible';

<Collapsible>
  <CollapsibleTrigger>Toggle</CollapsibleTrigger>
  <CollapsibleContent>Content</CollapsibleContent>
</Collapsible>
```

### ContextMenu (`ui/context-menu.tsx`)

Right-click context menus.

**Usage:**
```tsx
import { ContextMenu, ContextMenuContent, ContextMenuItem, ContextMenuTrigger } from '@/components/ui/context-menu';

<ContextMenu>
  <ContextMenuTrigger>Right click me</ContextMenuTrigger>
  <ContextMenuContent>
    <ContextMenuItem>Action 1</ContextMenuItem>
    <ContextMenuItem>Action 2</ContextMenuItem>
  </ContextMenuContent>
</ContextMenu>
```

### Dialog (`ui/dialog.tsx`)

Modal dialog windows.

**Usage:**
```tsx
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';

<Dialog>
  <DialogTrigger>
    <Button>Open Dialog</Button>
  </DialogTrigger>
  <DialogContent>
    <DialogHeader>
      <DialogTitle>Dialog Title</DialogTitle>
      <DialogDescription>Dialog description</DialogDescription>
    </DialogHeader>
    <Text>Dialog content</Text>
  </DialogContent>
</Dialog>
```

### DropdownMenu (`ui/dropdown-menu.tsx`)

Dropdown selection menus.

**Usage:**
```tsx
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@/components/ui/dropdown-menu';

<DropdownMenu>
  <DropdownMenuTrigger>
    <Button>Select option</Button>
  </DropdownMenuTrigger>
  <DropdownMenuContent>
    <DropdownMenuItem>Option 1</DropdownMenuItem>
    <DropdownMenuItem>Option 2</DropdownMenuItem>
  </DropdownMenuContent>
</DropdownMenu>
```

### HoverCard (`ui/hover-card.tsx`)

Cards that appear on hover.

**Usage:**
```tsx
import { HoverCard, HoverCardContent, HoverCardTrigger } from '@/components/ui/hover-card';

<HoverCard>
  <HoverCardTrigger>Hover me</HoverCardTrigger>
  <HoverCardContent>
    <Text>Card content</Text>
  </HoverCardContent>
</HoverCard>
```

### Icon (`ui/icon.tsx`)

SVG icon component with theme support.

**Usage:**
```tsx
import { Icon } from '@/components/ui/icon';

<Icon name="heart" size={24} />
```

### Input (`ui/input.tsx`)

Text input field with validation states.

**Props:**
```tsx
interface InputProps extends TextInputProps {
  className?: string;
  placeholderClassName?: string;
}
```

**Usage:**
```tsx
import { Input } from '@/components/ui/input';

<Input
  placeholder="Enter text"
  value={value}
  onChangeText={setValue}
/>
```

### Label (`ui/label.tsx`)

Form field labels.

**Usage:**
```tsx
import { Label } from '@/components/ui/label';

<Label htmlFor="email">Email address</Label>
<Input id="email" />
```

### Menubar (`ui/menubar.tsx`)

Horizontal menu bars.

**Usage:**
```tsx
import { Menubar, MenubarContent, MenubarItem, MenubarMenu, MenubarTrigger } from '@/components/ui/menubar';

<Menubar>
  <MenubarMenu>
    <MenubarTrigger>File</MenubarTrigger>
    <MenubarContent>
      <MenubarItem>New</MenubarItem>
      <MenubarItem>Open</MenubarItem>
    </MenubarContent>
  </MenubarMenu>
</Menubar>
```

### NativeOnlyAnimatedView (`ui/native-only-animated-view.tsx`)

Animated view for native platforms only.

### Popover (`ui/popover.tsx`)

Floating content overlays.

**Usage:**
```tsx
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';

<Popover>
  <PopoverTrigger>
    <Button>Open popover</Button>
  </PopoverTrigger>
  <PopoverContent>
    <Text>Popover content</Text>
  </PopoverContent>
</Popover>
```

### Progress (`ui/progress.tsx`)

Progress bar indicators.

**Usage:**
```tsx
import { Progress } from '@/components/ui/progress';

<Progress value={75} />
```

### RadioGroup (`ui/radio-group.tsx`)

Radio button groups.

**Usage:**
```tsx
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';

<RadioGroup value={value} onValueChange={setValue}>
  <RadioGroupItem value="option1" />
  <RadioGroupItem value="option2" />
</RadioGroup>
```

### Select (`ui/select.tsx`)

Select dropdown component.

**Usage:**
```tsx
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';

<Select>
  <SelectTrigger>
    <SelectValue placeholder="Select option" />
  </SelectTrigger>
  <SelectContent>
    <SelectItem value="option1">Option 1</SelectItem>
    <SelectItem value="option2">Option 2</SelectItem>
  </SelectContent>
</Select>
```

### Separator (`ui/separator.tsx`)

Visual separator lines.

**Usage:**
```tsx
import { Separator } from '@/components/ui/separator';

<Separator />
```

### Skeleton (`ui/skeleton.tsx`)

Loading placeholder components.

**Usage:**
```tsx
import { Skeleton } from '@/components/ui/skeleton';

<Skeleton className="h-4 w-32" />
```

### Switch (`ui/switch.tsx`)

Toggle switch component.

**Usage:**
```tsx
import { Switch } from '@/components/ui/switch';

<Switch checked={isEnabled} onCheckedChange={setIsEnabled} />
```

### Tabs (`ui/tabs.tsx`)

Tabbed navigation interface.

**Usage:**
```tsx
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';

<Tabs>
  <TabsList>
    <TabsTrigger value="tab1">Tab 1</TabsTrigger>
    <TabsTrigger value="tab2">Tab 2</TabsTrigger>
  </TabsList>
  <TabsContent value="tab1">Content 1</TabsContent>
  <TabsContent value="tab2">Content 2</TabsContent>
</Tabs>
```

### Text (`ui/text.tsx`)

Typography component with variants.

**Variants:**
- Various text sizes and weights

**Usage:**
```tsx
import { Text } from '@/components/ui/text';

<Text variant="h1">Heading</Text>
<Text variant="body">Body text</Text>
```

### Textarea (`ui/textarea.tsx`)

Multi-line text input.

**Usage:**
```tsx
import { Textarea } from '@/components/ui/textarea';

<Textarea
  placeholder="Enter long text"
  value={value}
  onChangeText={setValue}
/>
```

### Toggle (`ui/toggle.tsx`)

Toggle button component.

**Usage:**
```tsx
import { Toggle } from '@/components/ui/toggle';

<Toggle pressed={pressed} onPressedChange={setPressed}>
  Toggle me
</Toggle>
```

### ToggleGroup (`ui/toggle-group.tsx`)

Group of toggle buttons.

**Usage:**
```tsx
import { ToggleGroup, ToggleGroupItem } from '@/components/ui/toggle-group';

<ToggleGroup type="single" value={value} onValueChange={setValue}>
  <ToggleGroupItem value="option1">Option 1</ToggleGroupItem>
  <ToggleGroupItem value="option2">Option 2</ToggleGroupItem>
</ToggleGroup>
```

### Tooltip (`ui/tooltip.tsx`)

Informational tooltips on hover/focus.

**Usage:**
```tsx
import { Tooltip, TooltipContent, TooltipTrigger } from '@/components/ui/tooltip';

<Tooltip>
  <TooltipTrigger>
    <Button>Hover me</Button>
  </TooltipTrigger>
  <TooltipContent>
    <Text>Tooltip content</Text>
  </TooltipContent>
</Tooltip>
```

## Layout Components

### AppLayout (`components/layout/AppLayout.tsx`)

Main application layout with top bar and sidebar.

**Features:**
- Safe area handling
- Theme integration
- Responsive design
- Sidebar toggle

### TopAppBar (`components/layout/TopAppBar.tsx`)

Application header with navigation and search.

**Features:**
- Menu toggle
- Search functionality
- Notification icons
- User settings access

## Common Components

### LoadingScreen (`components/common/LoadingScreen.tsx`)

Full-screen loading indicator.

**Usage:**
```tsx
import { LoadingScreen } from '@/components/common/loading-screen';

<LoadingScreen message="Loading data..." />
```

### EmptyState (`components/common/EmptyState.tsx`)

Empty state display with actions.

**Usage:**
```tsx
import { EmptyState } from '@/components/common/empty-state';

<EmptyState
  title="No items found"
  description="Create your first item to get started"
  actionLabel="Create Item"
  onAction={handleCreate}
/>
```

## Usage Guidelines

### Component Naming Conventions
- Use kebab-case for file names: `sign-in-form.tsx`
- Export as named exports with PascalCase: `export function SignInForm`
- Import with destructuring: `import { SignInForm } from '@/components/sign-in-form'`

### Props Patterns
```tsx
interface ComponentProps extends React.ComponentProps<typeof BaseComponent> {
  variant?: 'option1' | 'option2';
  size?: 'sm' | 'md' | 'lg';
  disabled?: boolean;
  children?: React.ReactNode;
}
```

### Styling with Class Variance Authority (CVA)
All UI components use CVA for consistent variant management:
```tsx
const componentVariants = cva('base-classes', {
  variants: {
    variant: {
      default: 'default-styles',
      variant1: 'variant1-styles',
    },
    size: {
      sm: 'small-styles',
      lg: 'large-styles',
    },
  },
  defaultVariants: {
    variant: 'default',
    size: 'sm',
  },
});
```

### Theme Integration
- Components automatically respect light/dark themes
- Use semantic color tokens from the theme
- Support manual color overrides when needed

### Accessibility
- All components include proper accessibility labels
- Keyboard navigation support
- Screen reader compatibility
- Focus management for modals and overlays

### Platform Considerations
- Components are optimized for iOS, Android, and Web
- Platform-specific styling handled via `Platform.select()`
- Web-specific features like `focus-visible` and hover states

### Performance Best Practices
- Components are tree-shakeable
- Minimal re-renders with proper memoization
- Efficient style composition
- Lazy loading for heavy components

This component library provides a solid foundation for building consistent, accessible, and performant user interfaces in the Quester application.
