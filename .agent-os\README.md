# Quester Agent OS

Agent OS configuration for the Quester full-stack application (React Native Expo + Go Fiber).

## Overview

This Agent OS installation provides comprehensive AI-powered development assistance for the Quester project, including:

- **Code Generation**: Templates and patterns for React Native and Go development
- **Development Workflows**: Automated development environment setup and management
- **Deployment Automation**: Docker-based deployment workflows for multiple environments
- **Architecture Guidance**: Best practices and patterns specific to the tech stack

## Installation Verification

Run the verification script to ensure proper installation:

```bash
./.agent-os/verify-installation.sh
```

## Directory Structure

```
.agent-os/
├── config/
│   └── agent.json                    # Main Agent OS configuration
├── instructions/
│   ├── main.md                      # Core development patterns and guidelines
│   ├── frontend-react-native.md     # React Native Expo specific instructions
│   ├── backend-go-fiber.md          # Go Fiber backend patterns
│   └── docker-integration.md        # Docker and containerization guide
├── templates/
│   ├── react-native-component.tsx   # React Native component template
│   ├── go-handler.go               # Go HTTP handler template
│   └── go-model.go                 # GORM model template
├── tools/
│   └── code-generator.json         # Code generation configuration
├── workflows/
│   ├── development.json            # Development environment workflow
│   └── deployment.json             # Production deployment workflow
└── README.md                       # This file
```

## Quick Start

### 1. Development Environment

Start the development environment using the automated workflow:

```bash
# Switch to development environment
./env-switch.sh dev

# Start all services
./docker.sh dev up

# Verify all services are running
./docker.sh dev status
```

### 2. Code Generation

Use the templates for consistent code generation:

**React Native Component:**
- Template: `.agent-os/templates/react-native-component.tsx`
- Output: `client/app/components/{ComponentName}.tsx`

**Go Handler:**
- Template: `.agent-os/templates/go-handler.go`
- Output: `server/internal/handlers/{handlerName}.go`

**GORM Model:**
- Template: `.agent-os/templates/go-model.go`
- Output: `server/internal/models/{modelName}.go`

### 3. Development Patterns

Refer to the instructions for best practices:

- **React Native**: `.agent-os/instructions/frontend-react-native.md`
- **Go Fiber**: `.agent-os/instructions/backend-go-fiber.md`
- **Docker**: `.agent-os/instructions/docker-integration.md`

## Tech Stack Integration

### Frontend (React Native Expo)
- File-based routing with Expo Router
- Themed components for consistent UI
- TypeScript strict mode
- Platform-specific implementations (iOS/Android/Web)
- Hot reload with Metro bundler

### Backend (Go Fiber)
- RESTful API with `/api/v1/` prefix
- GORM ORM with PostgreSQL
- JWT authentication with Redis sessions
- WebSocket real-time features
- Structured logging with JSON format

### Infrastructure
- Multi-environment Docker Compose setup
- PostgreSQL + Redis data layer
- Nginx reverse proxy for production
- Hot reload for both frontend and backend
- Automated health checks and monitoring

## Workflows

### Development Workflow
Located at: `.agent-os/workflows/development.json`

Includes:
- Environment setup and validation
- Dependency installation
- Code quality checks (linting, formatting)
- Test execution
- Development server startup with hot reload

### Deployment Workflow
Located at: `.agent-os/workflows/deployment.json`

Includes:
- Pre-deployment validation
- Comprehensive testing
- Container building and optimization
- Database backup and migration
- Staged deployment (infrastructure → backend → frontend)
- Health checks and rollback strategy

## Environment Management

The Agent OS integrates with the existing environment management:

```bash
# Environment switching
./env-switch.sh dev         # Development
./env-switch.sh staging     # Staging
./env-switch.sh prod        # Production

# Docker operations
./docker.sh dev up          # Start development
./docker.sh dev down        # Stop all services
./docker.sh dev status      # Check status
./docker.sh dev logs        # View logs
```

## Code Generation Examples

### Generate a React Native Screen

```typescript
// Based on template: .agent-os/templates/react-native-component.tsx
export default function ProfileScreen() {
  const colorScheme = useColorScheme();

  return (
    <ThemedView style={styles.container}>
      <ThemedText type="title">User Profile</ThemedText>
      {/* Screen content */}
    </ThemedView>
  );
}
```

### Generate a Go Handler

```go
// Based on template: .agent-os/templates/go-handler.go
type UserHandler struct {
    db *gorm.DB
}

func (h *UserHandler) GetUsers(c *fiber.Ctx) error {
    var users []models.User
    // Handler implementation
}
```

### Generate a GORM Model

```go
// Based on template: .agent-os/templates/go-model.go
type User struct {
    ID        uint           `gorm:"primarykey" json:"id"`
    CreatedAt time.Time      `json:"created_at"`
    UpdatedAt time.Time      `json:"updated_at"`
    DeletedAt gorm.DeletedAt `gorm:"index" json:"deleted_at"`

    Username string `gorm:"uniqueIndex;not null" json:"username"`
    Email    string `gorm:"uniqueIndex;not null" json:"email"`
}
```

## Integration with Claude Code

The Agent OS is configured to work seamlessly with Claude Code via the permissions in `.claude/settings.local.json`:

```json
{
  "permissions": {
    "allow": [
      "Read(//c/Users/<USER>/.agent-os/instructions/**)",
      "Read(//c/Users/<USER>/.agent-os/**)"
    ]
  }
}
```

This allows Claude to access and utilize all Agent OS configurations, instructions, and templates for enhanced development assistance.

## Project Modules Supported

The Agent OS provides complete support for all Quester modules:

- **Authentication** (`/api/v1/auth/*`) - JWT tokens, user management
- **Users** (`/api/v1/users/*`) - Profile management, settings
- **Quests** (`/api/v1/quests/*`) - Quest management system
- **Tasks** (`/api/v1/tasks/*`) - Task operations and workflows
- **Messages** (`/api/v1/messages/*`) - Real-time messaging
- **Gamification** (`/api/v1/gamification/*`) - Points, achievements, leaderboards
- **Analytics** (`/api/v1/analytics/*`) - Metrics and insights
- **Roles & Permissions** (`/api/v1/roles/*`) - RBAC system
- **Learning Management** (`/api/v1/learning/*`) - Course and lesson management
- **Classifieds & Real Estate** (`/api/v1/classifieds/*`) - Property listings and marketplace

## Support and Documentation

For detailed implementation guidance, refer to:

1. **Main Instructions**: `.agent-os/instructions/main.md`
2. **Frontend Guide**: `.agent-os/instructions/frontend-react-native.md`
3. **Backend Guide**: `.agent-os/instructions/backend-go-fiber.md`
4. **Docker Guide**: `.agent-os/instructions/docker-integration.md`
5. **Project Documentation**: `settings/docs/` (comprehensive feature docs)

## Troubleshooting

If you encounter issues:

1. Run the verification script: `./.agent-os/verify-installation.sh`
2. Check Docker status: `docker info`
3. Verify environment: `./docker.sh dev status`
4. Review logs: `./docker.sh dev logs`

## Version

- **Agent OS Version**: 1.0.0
- **Quester Project**: Full-stack React Native + Go Fiber
- **Last Updated**: September 2025