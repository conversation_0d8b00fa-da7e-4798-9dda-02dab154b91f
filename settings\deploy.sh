#!/bin/bash

# Deploy script for Quester Docker environments
# Usage: ./deploy.sh <env> <command>

set -e

ENV=$1
COMMAND=$2

# Get the directory where the script is located
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" &> /dev/null && pwd)"
cd "$SCRIPT_DIR"

if [ -z "$ENV" ] || [ -z "$COMMAND" ]; then
    echo "Usage: ./deploy.sh <env> <command>"
    echo "Environments: dev, staging, prod"
    echo "Commands: up, down, restart, logs, build, server, client"
    exit 1
fi

# Validate environment
if [[ ! "$ENV" =~ ^(dev|staging|prod)$ ]]; then
    echo "Error: Environment must be dev, staging, or prod"
    exit 1
fi

# Set compose file based on environment
COMPOSE_FILE="docker-compose.${ENV}.yml"

if [ ! -f "$COMPOSE_FILE" ]; then
    echo "Error: $COMPOSE_FILE not found in $(pwd)"
    exit 1
fi

# Load environment variables
ENV_FILE=".env.${ENV}"
if [ -f "$ENV_FILE" ]; then
    echo "Loading environment from $ENV_FILE"
    set -a
    source "$ENV_FILE" 2>/dev/null || true
    set +a
fi

echo "Running command '$COMMAND' for environment '$ENV'"

case $COMMAND in
    up)
        echo "Stopping any existing containers..."
        docker-compose -f "$COMPOSE_FILE" down 2>/dev/null || true
        echo "Building and starting services..."
        docker-compose -f "$COMPOSE_FILE" up -d --build --remove-orphans
        echo "Environment '$ENV' started successfully"
        echo "Services are running in the background"
        sleep 3
        docker-compose -f "$COMPOSE_FILE" ps
        ;;
    down)
        docker-compose -f "$COMPOSE_FILE" down -v --remove-orphans
        echo "Environment '$ENV' stopped"
        ;;
    restart)
        docker-compose -f "$COMPOSE_FILE" down --remove-orphans
        docker-compose -f "$COMPOSE_FILE" up -d --build --remove-orphans
        echo "Environment '$ENV' restarted"
        ;;
    logs)
        docker-compose -f "$COMPOSE_FILE" logs -f
        ;;
    build)
        docker-compose -f "$COMPOSE_FILE" build --no-cache
        echo "Images rebuilt for environment '$ENV'"
        ;;
    server)
        docker-compose -f "$COMPOSE_FILE" up -d --build postgres redis
        sleep 2
        docker-compose -f "$COMPOSE_FILE" up -d --build server
        echo "Server services started for environment '$ENV'"
        ;;
    client)
        docker-compose -f "$COMPOSE_FILE" up -d --build client
        echo "Client service started for environment '$ENV'"
        ;;
    ps)
        docker-compose -f "$COMPOSE_FILE" ps
        ;;
    *)
        echo "Error: Unknown command '$COMMAND'"
        echo "Available commands: up, down, restart, logs, build, server, client, ps"
        exit 1
        ;;
esac