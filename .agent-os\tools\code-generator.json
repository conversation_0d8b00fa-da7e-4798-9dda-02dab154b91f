{"name": "code-generator", "version": "1.0.0", "description": "AI-powered code generator for Quester project", "capabilities": ["react-native-components", "go-handlers", "database-models", "api-endpoints", "docker-configs", "test-files"], "templates": {"react_native_component": {"template_path": "../templates/react-native-component.tsx", "output_pattern": "client/app/components/{ComponentName}.tsx", "variables": ["ComponentName", "props", "styles"]}, "go_handler": {"template_path": "../templates/go-handler.go", "output_pattern": "server/internal/handlers/{handlerName}.go", "variables": ["HandlerName", "handler<PERSON>ame", "ModelName"]}, "go_model": {"template_path": "../templates/go-model.go", "output_pattern": "server/internal/models/{modelName}.go", "variables": ["ModelName", "modelName", "tableName", "fields", "relationships"]}}, "generators": {"crud_api": {"description": "Generate complete CRUD API for a resource", "generates": ["go_model", "go_handler", "api_routes", "test_files"], "parameters": {"resource_name": {"type": "string", "required": true, "description": "Name of the resource (e.g., 'User', 'Quest')"}, "fields": {"type": "array", "required": true, "description": "List of model fields with types"}, "relationships": {"type": "array", "required": false, "description": "Model relationships (hasMany, belongsTo, etc.)"}}}, "react_native_screen": {"description": "Generate complete React Native screen with navigation", "generates": ["react_native_component", "navigation_config", "styles"], "parameters": {"screen_name": {"type": "string", "required": true, "description": "Name of the screen"}, "navigation_type": {"type": "enum", "values": ["stack", "tab", "modal"], "default": "stack"}}}}, "conventions": {"naming": {"go_files": "snake_case", "go_types": "PascalCase", "react_components": "PascalCase", "react_files": "kebab-case"}, "imports": {"go": ["github.com/gofiber/fiber/v2", "gorm.io/gorm", "quester/internal/models"], "react_native": ["react", "react-native", "@/components/ThemedText", "@/components/ThemedView"]}}, "validation": {"go_syntax": {"command": "go fmt", "args": ["-d"]}, "typescript_syntax": {"command": "npx tsc", "args": ["--noEmit"]}}}