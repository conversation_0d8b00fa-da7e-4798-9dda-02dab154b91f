# API Endpoints Reference

## Base Configuration

### API Base URL
```
Production: https://api.quester.app/api/v1
Development: http://localhost:8080/api/v1
```

### Authentication Header
```
Authorization: Bearer <jwt_token>
```

### Standard Response Format
```json
{
  "success": true,
  "data": {},
  "message": "Operation successful",
  "errors": null,
  "pagination": {
    "page": 1,
    "limit": 20,
    "total": 100,
    "pages": 5
  }
}
```

## Authentication Endpoints

### POST /auth/register
Register a new user account.

**Request Body:**
```json
{
  "email": "<EMAIL>",
  "username": "username",
  "first_name": "<PERSON>",
  "last_name": "<PERSON><PERSON>",
  "password": "securepassword123"
}
```

**Response (201):**
```json
{
  "success": true,
  "data": {
    "user": {
      "id": 1,
      "email": "<EMAIL>",
      "username": "username",
      "first_name": "<PERSON>",
      "last_name": "<PERSON><PERSON>",
      "avatar": null,
      "level": 1,
      "total_points": 0,
      "created_at": "2024-01-20T10:00:00Z"
    },
    "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "refresh_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
  }
}
```

### POST /auth/login
Authenticate user and get tokens.

**Request Body:**
```json
{
  "email": "<EMAIL>",
  "password": "securepassword123"
}
```

**Response (200):**
```json
{
  "success": true,
  "data": {
    "user": {
      "id": 1,
      "email": "<EMAIL>",
      "username": "username",
      "first_name": "John",
      "last_name": "Doe"
    },
    "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "refresh_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
  }
}
```

### POST /auth/refresh
Refresh access token using refresh token.

**Request Body:**
```json
{
  "refresh_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
}
```

### GET /auth/me
Get current authenticated user information.

**Headers:** `Authorization: Bearer <token>`

**Response (200):**
```json
{
  "success": true,
  "data": {
    "id": 1,
    "email": "<EMAIL>",
    "username": "username",
    "first_name": "John",
    "last_name": "Doe",
    "avatar": "https://example.com/avatars/1.jpg",
    "level": 3,
    "total_points": 1250,
    "is_verified": true,
    "role": "user",
    "created_at": "2024-01-20T10:00:00Z"
  }
}
```

## User Management Endpoints

### GET /users
List users with pagination and filters.

**Query Parameters:**
- `page` (integer): Page number (default: 1)
- `limit` (integer): Items per page (default: 20, max: 100)
- `search` (string): Search by username or email
- `role` (string): Filter by user role
- `is_active` (boolean): Filter by active status

**Response (200):**
```json
{
  "success": true,
  "data": [
    {
      "id": 1,
      "username": "john_doe",
      "email": "<EMAIL>",
      "first_name": "John",
      "last_name": "Doe",
      "avatar": "https://example.com/avatars/1.jpg",
      "level": 3,
      "total_points": 1250,
      "role": "user",
      "is_active": true,
      "created_at": "2024-01-20T10:00:00Z"
    }
  ],
  "pagination": {
    "page": 1,
    "limit": 20,
    "total": 50,
    "pages": 3
  }
}
```

### GET /users/:id
Get specific user details.

**Response (200):**
```json
{
  "success": true,
  "data": {
    "id": 1,
    "username": "john_doe",
    "email": "<EMAIL>",
    "first_name": "John",
    "last_name": "Doe",
    "avatar": "https://example.com/avatars/1.jpg",
    "level": 3,
    "total_points": 1250,
    "role": "user",
    "is_active": true,
    "created_at": "2024-01-20T10:00:00Z",
    "stats": {
      "quests_created": 5,
      "quests_completed": 12,
      "tasks_completed": 45,
      "achievements_unlocked": 8
    }
  }
}
```

### PUT /users/:id
Update user profile.

**Request Body:**
```json
{
  "first_name": "John",
  "last_name": "Smith",
  "avatar": "https://example.com/avatars/new.jpg"
}
```

## Quest Management Endpoints

### GET /quests
List quests with filters and pagination.

**Query Parameters:**
- `page`, `limit`: Pagination
- `status`: Filter by status (draft, active, in_progress, completed, paused, cancelled)
- `priority`: Filter by priority (low, medium, high, critical)
- `difficulty`: Filter by difficulty (beginner, intermediate, advanced, expert)
- `category_id`: Filter by category
- `created_by`: Filter by creator user ID
- `assigned_to`: Filter by assigned user ID
- `is_public`: Filter public quests (true/false)
- `search`: Search in title and description
- `tags`: Filter by tags (comma-separated)
- `due_date_before`: Filter quests due before date
- `due_date_after`: Filter quests due after date

**Response (200):**
```json
{
  "success": true,
  "data": [
    {
      "id": 1,
      "title": "Build Authentication System",
      "description": "Implement JWT-based authentication with user registration and login",
      "status": "in_progress",
      "priority": "high",
      "difficulty": "intermediate",
      "type": "project",
      "progress": 65,
      "points": 100,
      "estimated_hours": 40.0,
      "actual_hours": 25.5,
      "due_date": "2024-02-15T17:00:00Z",
      "start_date": "2024-01-15T09:00:00Z",
      "is_public": true,
      "tags": ["authentication", "backend", "security"],
      "created_by": {
        "id": 1,
        "username": "project_lead",
        "avatar": "https://example.com/avatars/1.jpg"
      },
      "category": {
        "id": 1,
        "name": "Backend Development",
        "color": "#4F46E5"
      },
      "task_count": 8,
      "completed_tasks": 5,
      "participant_count": 3,
      "created_at": "2024-01-10T10:00:00Z",
      "updated_at": "2024-01-18T14:30:00Z"
    }
  ],
  "pagination": {
    "page": 1,
    "limit": 20,
    "total": 25,
    "pages": 2
  }
}
```

### POST /quests
Create a new quest.

**Request Body:**
```json
{
  "title": "Build Authentication System",
  "description": "Implement JWT-based authentication with user registration and login",
  "priority": "high",
  "difficulty": "intermediate",
  "type": "project",
  "estimated_hours": 40.0,
  "due_date": "2024-02-15T17:00:00Z",
  "start_date": "2024-01-15T09:00:00Z",
  "points": 100,
  "is_public": true,
  "max_participants": 5,
  "tags": ["authentication", "backend", "security"],
  "category_id": 1,
  "custom_fields": {
    "client": "Acme Corp",
    "budget": 5000
  }
}
```

### GET /quests/:id
Get detailed quest information.

**Response (200):**
```json
{
  "success": true,
  "data": {
    "id": 1,
    "title": "Build Authentication System",
    "description": "Implement JWT-based authentication with user registration and login",
    "status": "in_progress",
    "priority": "high",
    "difficulty": "intermediate",
    "type": "project",
    "progress": 65,
    "points": 100,
    "estimated_hours": 40.0,
    "actual_hours": 25.5,
    "due_date": "2024-02-15T17:00:00Z",
    "start_date": "2024-01-15T09:00:00Z",
    "is_public": true,
    "tags": ["authentication", "backend", "security"],
    "created_by": {
      "id": 1,
      "username": "project_lead",
      "email": "<EMAIL>",
      "avatar": "https://example.com/avatars/1.jpg"
    },
    "category": {
      "id": 1,
      "name": "Backend Development",
      "color": "#4F46E5",
      "icon": "server"
    },
    "participants": [
      {
        "id": 2,
        "username": "backend_dev",
        "avatar": "https://example.com/avatars/2.jpg",
        "role": "developer",
        "joined_at": "2024-01-12T10:00:00Z"
      }
    ],
    "tasks": [
      {
        "id": 1,
        "title": "Setup JWT middleware",
        "status": "completed",
        "priority": "high",
        "assigned_to_id": 2,
        "progress": 100,
        "estimated_hours": 8.0,
        "actual_hours": 6.5,
        "due_date": "2024-01-18T17:00:00Z",
        "completed_at": "2024-01-17T15:30:00Z"
      }
    ],
    "recent_activity": [
      {
        "type": "task_completed",
        "user": "backend_dev",
        "description": "Completed task 'Setup JWT middleware'",
        "timestamp": "2024-01-17T15:30:00Z"
      }
    ],
    "created_at": "2024-01-10T10:00:00Z",
    "updated_at": "2024-01-18T14:30:00Z"
  }
}
```

### PUT /quests/:id
Update quest details.

**Request Body:**
```json
{
  "title": "Updated Quest Title",
  "description": "Updated description",
  "priority": "critical",
  "status": "active",
  "due_date": "2024-02-20T17:00:00Z"
}
```

### DELETE /quests/:id
Delete or archive a quest.

**Response (200):**
```json
{
  "success": true,
  "message": "Quest deleted successfully"
}
```

### POST /quests/:id/join
Join a public quest as a participant.

**Response (200):**
```json
{
  "success": true,
  "message": "Successfully joined quest"
}
```

### POST /quests/:id/leave
Leave a quest as a participant.

**Response (200):**
```json
{
  "success": true,
  "message": "Successfully left quest"
}
```

### GET /quests/:id/participants
Get quest participants list.

**Response (200):**
```json
{
  "success": true,
  "data": [
    {
      "id": 2,
      "username": "backend_dev",
      "email": "<EMAIL>",
      "avatar": "https://example.com/avatars/2.jpg",
      "role": "developer",
      "joined_at": "2024-01-12T10:00:00Z",
      "contribution": {
        "tasks_assigned": 3,
        "tasks_completed": 2,
        "hours_logged": 15.5
      }
    }
  ]
}
```

### POST /quests/:id/participants
Add participants to quest.

**Request Body:**
```json
{
  "user_ids": [2, 3, 4],
  "message": "Welcome to the project team!"
}
```

## Task Management Endpoints

### GET /tasks
List tasks with filters.

**Query Parameters:**
- `quest_id`: Filter by quest
- `assigned_to`: Filter by assigned user
- `status`: Filter by status
- `priority`: Filter by priority
- `due_date_before`: Tasks due before date
- `overdue`: Show only overdue tasks (true/false)

**Response (200):**
```json
{
  "success": true,
  "data": [
    {
      "id": 1,
      "quest_id": 1,
      "title": "Setup JWT middleware",
      "description": "Implement JWT authentication middleware for API routes",
      "status": "completed",
      "priority": "high",
      "type": "feature",
      "progress": 100,
      "points": 20,
      "estimated_hours": 8.0,
      "actual_hours": 6.5,
      "remaining_hours": 0.0,
      "due_date": "2024-01-18T17:00:00Z",
      "start_date": "2024-01-15T09:00:00Z",
      "completed_at": "2024-01-17T15:30:00Z",
      "assigned_to": {
        "id": 2,
        "username": "backend_dev",
        "avatar": "https://example.com/avatars/2.jpg"
      },
      "quest": {
        "id": 1,
        "title": "Build Authentication System"
      },
      "dependencies": [],
      "can_start": true,
      "is_overdue": false,
      "created_at": "2024-01-15T10:00:00Z",
      "updated_at": "2024-01-17T15:30:00Z"
    }
  ]
}
```

### POST /tasks
Create a new task.

**Request Body:**
```json
{
  "quest_id": 1,
  "title": "Setup JWT middleware",
  "description": "Implement JWT authentication middleware for API routes",
  "priority": "high",
  "type": "feature",
  "estimated_hours": 8.0,
  "points": 20,
  "due_date": "2024-01-18T17:00:00Z",
  "assigned_to_id": 2,
  "depends_on_task_ids": [3, 4],
  "custom_fields": {
    "testing_required": true,
    "code_review_needed": true
  }
}
```

### GET /tasks/:id
Get detailed task information.

### PUT /tasks/:id
Update task details.

### DELETE /tasks/:id
Delete a task.

### POST /tasks/:id/assign
Assign task to a user.

**Request Body:**
```json
{
  "assignee_id": 2,
  "message": "Please handle this high-priority task"
}
```

### POST /tasks/:id/complete
Mark task as completed.

**Request Body:**
```json
{
  "completion_notes": "Task completed successfully with all tests passing",
  "actual_hours": 6.5
}
```

## Message & Communication Endpoints

### GET /messages
Get message history.

**Query Parameters:**
- `conversation_id`: Filter by conversation
- `sender_id`: Filter by sender
- `receiver_id`: Filter by receiver
- `type`: Filter by message type
- `after`: Messages after timestamp
- `before`: Messages before timestamp

**Response (200):**
```json
{
  "success": true,
  "data": [
    {
      "id": 1,
      "conversation_id": 1,
      "sender_id": 1,
      "receiver_id": 2,
      "content": "Hey, how's the authentication task going?",
      "message_type": "text",
      "is_read": true,
      "is_delivered": true,
      "created_at": "2024-01-20T14:30:00Z",
      "read_at": "2024-01-20T14:32:00Z",
      "sender": {
        "id": 1,
        "username": "project_lead",
        "avatar": "https://example.com/avatars/1.jpg"
      }
    }
  ]
}
```

### POST /messages
Send a new message.

**Request Body:**
```json
{
  "receiver_id": 2,
  "content": "Hey, how's the authentication task going?",
  "message_type": "text",
  "conversation_id": 1
}
```

### PUT /messages/:id/read
Mark message as read.

**Response (200):**
```json
{
  "success": true,
  "message": "Message marked as read"
}
```

### GET /conversations
Get user conversations.

**Response (200):**
```json
{
  "success": true,
  "data": [
    {
      "id": 1,
      "type": "direct",
      "name": null,
      "last_message": {
        "id": 5,
        "content": "Sounds good, let's do it!",
        "sender": {
          "username": "backend_dev"
        },
        "created_at": "2024-01-20T15:45:00Z"
      },
      "participants": [
        {
          "id": 1,
          "username": "project_lead",
          "avatar": "https://example.com/avatars/1.jpg"
        },
        {
          "id": 2,
          "username": "backend_dev",
          "avatar": "https://example.com/avatars/2.jpg"
        }
      ],
      "unread_count": 2,
      "updated_at": "2024-01-20T15:45:00Z"
    }
  ]
}
```

## Notification Endpoints

### GET /notifications
Get user notifications.

**Query Parameters:**
- `is_read`: Filter by read status
- `type`: Filter by notification type
- `priority`: Filter by priority

**Response (200):**
```json
{
  "success": true,
  "data": [
    {
      "id": 1,
      "type": "task_assigned",
      "title": "New Task Assigned",
      "message": "You have been assigned to task 'Setup JWT middleware'",
      "is_read": false,
      "priority": "normal",
      "data": {
        "task_id": 1,
        "quest_id": 1,
        "assigned_by": "project_lead"
      },
      "created_at": "2024-01-20T10:00:00Z"
    }
  ]
}
```

### PUT /notifications/:id/read
Mark notification as read.

### PUT /notifications/read-all
Mark all notifications as read.

### DELETE /notifications/:id
Delete a notification.

## File Upload Endpoints

### POST /upload/avatar
Upload user avatar.

**Request:** Multipart form data with `file` field

**Response (200):**
```json
{
  "success": true,
  "data": {
    "url": "https://example.com/uploads/avatars/user-1-1642678800.jpg",
    "filename": "user-1-1642678800.jpg",
    "size": 245760,
    "mime_type": "image/jpeg"
  }
}
```

### POST /upload/attachment
Upload file attachment.

**Request:** Multipart form data with `file` field and optional metadata

**Query Parameters:**
- `quest_id`: Associate with quest
- `task_id`: Associate with task
- `message_id`: Associate with message

## Analytics Endpoints

### GET /analytics/dashboard
Get dashboard analytics data.

**Response (200):**
```json
{
  "success": true,
  "data": {
    "user_stats": {
      "total_quests": 15,
      "completed_quests": 8,
      "total_tasks": 45,
      "completed_tasks": 32,
      "total_points": 1250,
      "current_level": 3
    },
    "recent_activity": [
      {
        "type": "quest_completed",
        "description": "Completed quest 'Build Authentication System'",
        "timestamp": "2024-01-20T16:00:00Z"
      }
    ],
    "progress_chart": {
      "labels": ["Jan 1", "Jan 7", "Jan 14", "Jan 21"],
      "completed_tasks": [2, 5, 8, 12],
      "total_points": [50, 150, 300, 500]
    }
  }
}
```

### GET /analytics/quests/:id
Get quest-specific analytics.

### GET /analytics/productivity
Get productivity metrics.

## Error Responses

### Standard Error Format
```json
{
  "success": false,
  "data": null,
  "message": "Error description",
  "errors": {
    "field_name": ["Error message for field"]
  },
  "error_code": "VALIDATION_ERROR"
}
```

### Common HTTP Status Codes
- `200`: Success
- `201`: Created
- `400`: Bad Request / Validation Error
- `401`: Unauthorized
- `403`: Forbidden
- `404`: Not Found
- `409`: Conflict
- `422`: Unprocessable Entity
- `429`: Too Many Requests
- `500`: Internal Server Error

### Example Error Responses

**Validation Error (400):**
```json
{
  "success": false,
  "message": "Validation failed",
  "errors": {
    "email": ["Email is required"],
    "password": ["Password must be at least 8 characters"]
  },
  "error_code": "VALIDATION_ERROR"
}
```

**Unauthorized (401):**
```json
{
  "success": false,
  "message": "Invalid or expired token",
  "error_code": "UNAUTHORIZED"
}
```

**Not Found (404):**
```json
{
  "success": false,
  "message": "Quest not found",
  "error_code": "RESOURCE_NOT_FOUND"
}
```

## Rate Limiting

### Headers
- `X-RateLimit-Limit`: Request limit per window
- `X-RateLimit-Remaining`: Remaining requests in current window
- `X-RateLimit-Reset`: Time when rate limit resets (Unix timestamp)

### Limits
- **Authentication endpoints**: 5 requests per minute
- **General API**: 100 requests per minute
- **File uploads**: 10 requests per minute
- **WebSocket connections**: 10 per minute

## Testing Examples

### cURL Examples

**Register User:**
```bash
curl -X POST http://localhost:8080/api/v1/auth/register \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "username": "testuser",
    "first_name": "Test",
    "last_name": "User",
    "password": "password123"
  }'
```

**Create Quest:**
```bash
curl -X POST http://localhost:8080/api/v1/quests \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer $JWT_TOKEN" \
  -d '{
    "title": "Test Quest",
    "description": "A test quest for API testing",
    "priority": "medium",
    "due_date": "2024-02-01T17:00:00Z"
  }'
```

**Get User's Quests:**
```bash
curl -X GET "http://localhost:8080/api/v1/quests?status=active&page=1&limit=10" \
  -H "Authorization: Bearer $JWT_TOKEN"
```

This comprehensive API reference covers all current endpoints with detailed request/response examples for easy implementation and testing.