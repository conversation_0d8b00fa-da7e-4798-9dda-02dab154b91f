# Authentication Flow Implementation

## Core Authentication Logic

### App Initialization (_layout.tsx)
```typescript
function AppContent() {
  const dispatch = useAppDispatch();
  const isInitialized = useAppSelector(selectIsInitialized);
  const isAuthenticated = useAppSelector(selectIsAuthenticated);
  const router = useRouter();
  const segments = useSegments();

  // Initialize auth on app start
  useEffect(() => {
    dispatch(initializeAuth());
  }, [dispatch]);

  // Handle navigation based on authentication state
  useEffect(() => {
    if (!isInitialized || !fontsLoaded) return;

    const inAuthGroup = segments[0] === '(auth)';

    if (isAuthenticated && inAuthGroup) {
      router.replace('/(tabs)/');      // Go to dashboard
    } else if (!isAuthenticated && !inAuthGroup) {
      router.replace('/(auth)/login'); // Go to login
    }
  }, [isInitialized, isAuthenticated, segments, router, fontsLoaded]);
}
```

## Redux Authentication Slice

### State Structure
```typescript
interface AuthState {
  user: User | null;
  isAuthenticated: boolean;
  isInitialized: boolean;
  accessToken: string | null;
  refreshToken: string | null;
  isLoading: boolean;
  error: string | null;
}
```

### Critical Selectors (with null safety)
```typescript
export const selectUser = (state: any) => state.auth?.user || null;
export const selectIsAuthenticated = (state: any) => state.auth?.isAuthenticated || false;
export const selectIsInitialized = (state: any) => state.auth?.isInitialized || false;
export const selectIsLoading = (state: any) => state.auth?.isLoading || false;
```

### Key Async Thunks
```typescript
// Initialize authentication
export const initializeAuth = createAsyncThunk(
  'auth/initialize',
  async (_, { dispatch, rejectWithValue }) => {
    try {
      const tokens = await tokenStorage.getTokens();
      if (tokens.accessToken) {
        // Validate token with server
        const user = await authAPI.getMe(tokens.accessToken);
        return { user, ...tokens };
      }
      return null;
    } catch (error) {
      await tokenStorage.clearTokens();
      return rejectWithValue('Token validation failed');
    }
  }
);

// Login user
export const login = createAsyncThunk(
  'auth/login',
  async (credentials: LoginRequest, { rejectWithValue }) => {
    try {
      const response = await authAPI.login(credentials);
      await tokenStorage.setTokens(response.accessToken, response.refreshToken);
      return response;
    } catch (error: any) {
      return rejectWithValue(error.message || 'Login failed');
    }
  }
);
```

## Token Storage Service

### Secure Storage Implementation
```typescript
// services/storage/tokenStorage.ts
export const tokenStorage = {
  async setTokens(accessToken: string, refreshToken: string): Promise<void> {
    await SecureStore.setItemAsync('accessToken', accessToken);
    await SecureStore.setItemAsync('refreshToken', refreshToken);
  },

  async getTokens(): Promise<{ accessToken: string | null; refreshToken: string | null }> {
    const [accessToken, refreshToken] = await Promise.all([
      SecureStore.getItemAsync('accessToken'),
      SecureStore.getItemAsync('refreshToken')
    ]);
    return { accessToken, refreshToken };
  },

  async clearTokens(): Promise<void> {
    await Promise.all([
      SecureStore.deleteItemAsync('accessToken'),
      SecureStore.deleteItemAsync('refreshToken')
    ]);
  }
};
```

## API Authentication Service

### Auth API Implementation
```typescript
// services/api/authAPI.ts
export const authAPI = {
  async login(credentials: LoginRequest): Promise<AuthResponse> {
    const response = await api.post('/auth/login', credentials);
    return response.data;
  },

  async register(userData: RegisterRequest): Promise<AuthResponse> {
    const response = await api.post('/auth/register', userData);
    return response.data;
  },

  async getMe(token: string): Promise<User> {
    const response = await api.get('/auth/me', {
      headers: { Authorization: `Bearer ${token}` }
    });
    return response.data;
  },

  async refreshToken(refreshToken: string): Promise<AuthResponse> {
    const response = await api.post('/auth/refresh', { refreshToken });
    return response.data;
  }
};
```

## Route Protection

### File Structure for Authentication
```
app/
├── (auth)/              # Public routes
│   ├── _layout.tsx     # Auth layout
│   ├── login.tsx       # Login screen
│   └── register.tsx    # Register screen
├── (tabs)/             # Protected routes
│   ├── _layout.tsx     # Tab layout
│   ├── index.tsx       # Dashboard
│   └── ...
└── _layout.tsx         # Root layout with auth logic
```

### Navigation Logic
- `(auth)` group: Accessible when NOT authenticated
- `(tabs)` group: Accessible when authenticated
- Root layout handles automatic redirects

## Server-Side JWT Middleware

### JWT Token Validation
```go
func AuthMiddleware() gin.HandlerFunc {
    return func(c *gin.Context) {
        authHeader := c.GetHeader("Authorization")
        if authHeader == "" {
            c.JSON(401, gin.H{"error": "Authorization header required"})
            c.Abort()
            return
        }

        tokenString := strings.TrimPrefix(authHeader, "Bearer ")
        claims, err := ValidateToken(tokenString)
        if err != nil {
            c.JSON(401, gin.H{"error": "Invalid token"})
            c.Abort()
            return
        }

        c.Set("user_id", claims.UserID)
        c.Set("user_email", claims.Email)
        c.Set("user_role", claims.Role)
        c.Next()
    }
}
```

## Critical Implementation Notes

### ⚠️ Common Pitfalls
1. **Selector Safety**: Always use null-safe operators (`state.auth?.property || defaultValue`)
2. **Initialization Order**: Auth must initialize before navigation logic
3. **Token Refresh**: Implement automatic token refresh in API interceptors
4. **Error Handling**: Clear tokens on any authentication error

### ✅ Best Practices
1. Use secure storage for tokens (Expo SecureStore)
2. Validate tokens on app start
3. Implement proper loading states
4. Handle authentication errors gracefully
5. Use Redux for centralized auth state

This authentication flow ensures secure, reliable user authentication with proper state management and navigation.