# Project Structure & Organization

## Client Structure (React Native + Expo)

```
client/
├── app/                          # Expo Router pages
│   │   └── ...
├── components/                   # Reusable components
│   │   └── ...
├── constants/                    # Constants and types
│   │   └── ...
├── hooks/
│   │   └── ...
├── services/                    # External services
│   ├── api/                     # API services
│   │   └── ...
│   ├── storage/                 # Storage services
│   │   └── ...
│   └── websocket/               # WebSocket services
│   │   └── ...
├── store/                       # Redux store
│   │   └── ...
├── assets/                      # Static assets
│   │   └── ...
├── .env                        # Environment variables
├── package.json
└── tsconfig.json
```

## Server Structure (Go + Gin)

```
server/
├── cmd/
│   └── server/
│       └── main.go                   # Application entry point
├── internal/
│   ├── config/
│   │   └── ...
│   ├── database/
│   │   └── ...
│   ├── handlers/   
│   │   └── ...                 
│   ├── middleware/
│   │   └── ...
│   ├── routes/
│   │   └── ...
│   ├── services/
│   │   └── ...
│   ├── utils/
│   │   └── ...
│   └── websocket/
│       └── ...
├── uploads/                          # File uploads
├── go.mod                           # Go module definition
├── go.sum                           # Dependency checksums
├── .env                             # Environment variables
```

## File Naming Conventions

### ⚠️ Critical: Component Naming
```typescript
// ✅ Correct file names (kebab-case)
themed-view.tsx
themed-text.tsx
loading-screen.tsx

// ✅ Correct imports
import ThemedView from '../components/themed-view';
import ThemedText from '../components/themed-text';

// ❌ Wrong - will cause Metro errors
ThemedView.tsx  // PascalCase files don't exist
```

### Go File Conventions
```go
// File names: snake_case or single words
auth.go
user_service.go
websocket_hub.go

// Package structure
package handlers  // lowercase
package models
package services
```

## Key Configuration Files

### Client package.json (essential dependencies)
```json
{
  "dependencies": {
    "expo": "~51.0.0",
    "react": "18.2.0",
    "react-native": "0.74.5",
    "@expo/router": "~3.5.0",
    "@reduxjs/toolkit": "^2.0.0",
    "react-redux": "^9.0.0",
    "redux-persist": "^6.0.0",
    "axios": "^1.6.0",
    "expo-secure-store": "~13.0.0",
    "@react-navigation/native": "^6.0.0"
  }
}
```

### Server go.mod (essential modules)
```go
module quester-server

go 1.21

require (
    github.com/gin-gonic/gin v1.9.1
    github.com/lib/pq v1.10.9
    gorm.io/gorm v1.25.5
    github.com/golang-jwt/jwt/v5 v5.0.0
    github.com/gorilla/websocket v1.5.0
    github.com/joho/godotenv v1.4.0
)
```

## Environment Files

### Client .env
```bash
# API Configuration
EXPO_PUBLIC_API_URL=http://localhost:8080/api/v1

# Optional: WebSocket URL
EXPO_PUBLIC_WS_URL=ws://localhost:8080/ws
```

### Server .env
```bash
# Server Configuration
PORT=8080

# Database
DATABASE_URL=postgresql://username:password@localhost:5432/quester

# JWT
JWT_SECRET=your-super-secret-jwt-key-here
JWT_REFRESH_SECRET=your-refresh-secret-here

# Redis (optional)
REDIS_URL=redis://localhost:6379

# File Uploads
UPLOAD_PATH=./uploads
MAX_FILE_SIZE=10485760

# CORS
ALLOWED_ORIGINS=http://localhost:3000,http://localhost:8081
```

## Critical Implementation Notes

### File Organization Rules
1. **Group by feature** not by file type
2. **Shared components** go in `common/`
3. **API services** organized by domain
4. **Redux slices** match feature domains

### Import Path Patterns
```typescript
// ✅ Correct relative imports
import { useAppDispatch } from '../store';
import ThemedView from '../components/themed-view';
import { authAPI } from '../services/api/authAPI';

// ✅ Absolute imports (if configured)
import { useAppDispatch } from '@/store';
import ThemedView from '@/components/themed-view';
```

### Go Package Organization
```go
// handlers/auth.go
package handlers

import (
    "quester-server/internal/services"
    "quester-server/internal/models"
)

// Clear separation of concerns
// Handlers → Services → Models → Database
```

This structure provides clear separation of concerns and makes the codebase easily navigable for both development and regeneration purposes.