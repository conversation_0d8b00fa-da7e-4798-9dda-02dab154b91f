# Quester

> **AI Agent Reference**: A comprehensive full-stack application with modular architecture supporting multiple business domains including task management, learning systems, and real estate management.

## Project Classification
- **Type**: Full-stack mobile/web application
- **Primary Domain**: Multi-tenant SaaS platform
- **Architecture Pattern**: Microservices-ready monolith
- **Deployment**: Containerized multi-environment
- **Real-time Capabilities**: WebSocket-enabled
- **AI/ML Ready**: Analytics and recommendation system foundation

---

## Technology Stack

### Frontend (Client)
```yaml
Framework: React Native Expo
Language: TypeScript
Routing: File-based (Expo Router)
State Management: React hooks + Context API
UI Framework: Custom themed components
Platform Support: iOS, Android, Web
```

### Backend (Server)
```yaml
Framework: Go + Fiber
ORM: GORM
Authentication: JWT tokens
Real-time: WebSocket
API Pattern: RESTful with /api/v1 prefix
Logging: Structured JSON (logrus)
```

### Database & Infrastructure
```yaml
Primary DB: PostgreSQL
Cache/Sessions: Redis
Containerization: Dock<PERSON> + Docker Compose
Reverse Proxy: Nginx
Hot Reload: Air (Go) + Metro (React Native)
```

---

## System Overview

Quester is a modern, scalable application platform that combines:

1. **Task & Project Management** - Quest-based workflow system
2. **Learning Management System** - Educational content delivery
3. **Real Estate & Classifieds** - Property and advertisement management
4. **Gamification Engine** - Points, achievements, and leaderboards
5. **Real-time Communication** - Messaging and live updates
6. **Analytics Platform** - User engagement and performance metrics

---

## Architecture Summary

- **Frontend**: React Native Expo with TypeScript and file-based routing
- **Backend**: Go + Fiber framework with GORM ORM
- **Database**: PostgreSQL with Redis for caching
- **Infrastructure**: Docker Compose with multi-environment support
- **Real-time**: WebSocket integration for live updates

---

## Core Features & Modules

> **For AI Agents/Models:** Each module below is mapped to API endpoints and database tables. Use the provided endpoint patterns and table names for code generation, automation, or reference.

### 1. Authentication & User Management

**Endpoints**: `/api/v1/auth/*`, `/api/v1/users/*`

- Secure login/logout with JWT tokens
- User registration with validation
- Password recovery functionality
- Profile management and customization

### 2. Task & Quest Management

**Endpoints**: `/api/v1/quests/*`, `/api/v1/tasks/*`

- Create and manage quests
- Assign tasks to users and teams
- Visual progress tracking
- Multi-user collaboration features

### 3. Real-time Messaging

**Endpoints**: `/api/v1/messages/*`, `/ws/:id`

- Direct user-to-user messaging
- Group conversations
- WebSocket-powered real-time delivery
- Message history and persistence

### 4. Gamification System

**Endpoints**: `/api/v1/gamification/*`

#### 🌟 Points & Rewards

- Task completion rewards
- Quest milestone bonuses
- Collaboration incentives
- Daily activity streaks

#### 🏆 Achievements

- Progress-based unlocks
- Skill recognition badges
- Team collaboration rewards
- Special event achievements

#### 📊 Leaderboards

- Global user rankings
- Category-specific boards
- Team competitions
- Time-limited challenges

#### 🎁 Reward System

- Virtual badges and recognition
- Profile customization unlocks
- Premium feature access
- Public acknowledgments

### 5. Analytics & Insights

**Endpoints**: `/api/v1/analytics/*`

- Real-time activity tracking
- Performance metrics visualization
- Customizable dashboard widgets
- User engagement analytics

### 6. Notification System

**Endpoints**: `/api/v1/notifications/*`

- In-app notifications
- Push notifications (mobile)
- Email alerts
- Customizable preferences

### 7. Roles & Permissions Management

**Endpoints**: `/api/v1/roles/*`, `/api/v1/permissions/*`, `/api/v1/users/*/roles`

- Role-Based Access Control (RBAC) system
- Dynamic permission assignment and management
- User role assignments with expiration
- Fine-grained resource and action permissions
- System and custom role definitions
- Permission caching for performance
- Audit trail for role changes
- Multi-level access control across all modules

### 8. Learning & Teaching Management

**Endpoints**: `/api/v1/learning/*`, `/api/v1/courses/*`

- Course creation and management
- Interactive lesson planning
- Student progress tracking
- Assignment distribution and grading
- Virtual classroom integration
- Learning resource library
- Skill assessment tools
- Certification management
- Instructor dashboards
- Student performance analytics

### 9. Classifieds, Ads & Real Estate Management

**Endpoints**: `/api/v1/classifieds/*`, `/api/v1/properties/*`

- Property listing and search
- Advanced filtering and categorization
- Photo galleries and virtual tours
- Location-based discovery
- Price tracking and market analysis
- Contact management system
- Appointment scheduling
- Document sharing and storage
- Lead generation and CRM integration
- Multi-category classified ads (vehicles, jobs, services, etc.)
- Real estate agent profiles and ratings
- Mortgage calculator and financing tools

---

## API Reference & Development Guide

### API Endpoints Structure

```yaml
Base URL: http://localhost:8000/api/v1
Authentication: Bearer JWT token
Content-Type: application/json
WebSocket: ws://localhost:8000/ws/:id
```

### Core API Modules

| Module         | Endpoint Pattern           | Table(s)         | Description                       | Documentation Reference          |
|---------------|---------------------------|------------------|-----------------------------------|----------------------------------|
| Authentication| `/api/v1/auth/*`          | `users`          | Login, registration, token mgmt   | `settings/docs/authentication.md` |
| Users         | `/api/v1/users/*`         | `users`          | User profiles, settings           | `settings/docs/authentication.md` |
| Quests        | `/api/v1/quests/*`        | `quests`         | Quest creation, assignment        | `settings/docs/quest-management.md` |
| Tasks         | `/api/v1/tasks/*`         | `tasks`          | Task operations, status updates   | `settings/docs/quest-management.md` |
| Messages      | `/api/v1/messages/*`      | `messages`       | Direct messaging, conversations   | `settings/docs/messaging-system.md` |
| Gamification  | `/api/v1/gamification/*`  | `achievements`, `leaderboards`, `rewards` | Points, achievements, leaderboards | `settings/docs/gamification.md` |
| Analytics     | `/api/v1/analytics/*`     | `analytics`      | Metrics, reports, insights        | `settings/docs/database-api.md` |
| Notifications | `/api/v1/notifications/*` | `notifications`  | Push, email, in-app alerts        | `settings/docs/messaging-system.md` |
| Roles & Permissions | `/api/v1/roles/*`, `/api/v1/permissions/*` | `roles`, `permissions`, `user_roles`, `role_permissions` | RBAC system, access control | `settings/docs/roles-permissions.md` |
| Learning      | `/api/v1/learning/*`      | `courses`, `lessons`, `assignments`, `certifications` | Courses, lessons, assessments | `settings/docs/learning-management.md` |
| Classifieds   | `/api/v1/classifieds/*`   | `classifieds`, `properties`, `categories`, `agents` | Ads, properties, listings | `settings/docs/classifieds-properties.md` |

---

## Database Schema Overview

```yaml
Core Tables:
  - users (authentication, profiles)
  - quests (quest management)
  - tasks (task management)
  - messages (messaging)
  - achievements (gamification)
  - leaderboards (gamification)
  - rewards (gamification)
  - analytics (metrics)
  - notifications (alerts system)
  - roles (role definitions)
  - permissions (permission definitions)
  - user_roles (user-role assignments)
  - role_permissions (role-permission assignments)
  - courses (learning)
  - lessons (learning)
  - assignments (learning)
  - certifications (learning)
  - classifieds (ads)
  - properties (real estate)
  - categories (classifieds/real estate)
  - agents (real estate)

Relationships:
  - User hasMany: Quests, Tasks, Messages, Achievements, Courses, Properties, UserRoles
  - User belongsToMany: Roles (through UserRoles)
  - Role hasMany: UserRoles, RolePermissions
  - Role belongsToMany: Users (through UserRoles), Permissions (through RolePermissions)
  - Permission belongsToMany: Roles (through RolePermissions)
  - Course hasMany: Lessons, Assignments, Certifications
  - Property belongsTo: User, Category, Agent
```

---

## Environment Configuration

```yaml
Development:
  - PostgreSQL: localhost:5432
  - Redis: localhost:6379
  - Server: localhost:8000
  - Client: localhost:19000
  - MailHog: localhost:8025

Staging/Production:
  - Nginx reverse proxy
  - Automated backups
  - Multi-replica deployment
```

---

## 🤖 AI Agent & Documentation Reference

### Comprehensive Feature Documentation

The `settings/docs/` folder contains **complete implementation documentation** for all system features, designed specifically for AI agents and rapid code generation:

```
settings/docs/
├── authentication.md              # JWT auth, bcrypt, Redis sessions, middleware
├── quest-management.md           # Quest/Task models, progress tracking, workflows
├── messaging-system.md           # WebSocket hub, real-time communication patterns
├── gamification.md               # Achievement/Badge/Level systems, point mechanics
├── learning-management.md        # Course/Lesson/Assignment models, LMS features
├── classifieds-properties.md     # Real estate, marketplace, search functionality
├── ui-ux-patterns.md            # Responsive design, theming, glass components
├── database-api.md              # GORM patterns, API design, caching strategies
└── deployment-infrastructure.md  # Docker, multi-env deployment, CI/CD
```

### 📋 What Each Documentation Contains

Each file provides **complete implementation reference** including:

- **🏗️ Model Definitions**: Full GORM models with relationships and tags
- **🔌 API Endpoints**: Complete endpoint patterns with request/response examples
- **💻 Implementation Examples**: Both Go backend and React Native frontend code
- **🧪 Testing Patterns**: cURL examples and test implementations
- **📁 File Structure**: Exact file organization and naming conventions
- **⚡ Best Practices**: Performance optimizations and architectural patterns
- **🔗 Integration Points**: How features connect and communicate

### 🎯 AI Agent Usage Guidelines

**For Code Generation & Reference:**

1. **Feature Implementation**: Use specific docs (e.g., `authentication.md`) for complete feature implementation patterns
2. **API Development**: Reference `database-api.md` for consistent API patterns and database operations
3. **Frontend Components**: Use `ui-ux-patterns.md` for responsive design and themed component patterns
4. **Deployment**: Use `deployment-infrastructure.md` for Docker, environment management, and production setup
5. **Integration**: Cross-reference multiple docs for feature combinations (e.g., quest + gamification)

**Quick Reference Patterns:**

- **Endpoint Patterns**: `/api/v1/{module}/*` - All endpoints follow this structure
- **Model Relationships**: User hasMany (Quests, Properties, Courses, UserRoles), Role belongsToMany (Users, Permissions), Quest hasMany (Tasks)
- **Authentication**: JWT tokens with Redis sessions, middleware patterns
- **Real-time**: WebSocket hub pattern at `/ws/:id` endpoints
- **Responsive UI**: Mobile-first with tablet/desktop adaptive navigation
- **Environment**: Docker-based with dev/staging/prod configurations

### 🔄 Documentation-Code Sync

The documentation reflects the **current codebase state** as of September 2025:

- **Backend**: Go 1.25+ with Fiber framework, GORM ORM, PostgreSQL + Redis
- **Frontend**: React Native Expo with TypeScript, file-based routing, responsive design
- **Infrastructure**: Docker Compose with multi-environment support and hot reload
- **Architecture**: Full-stack with WebSocket real-time features and comprehensive gamification

**For AI Agents**: These docs enable **immediate code generation** without codebase analysis - all patterns, structures, and implementations are documented with working examples.

### 📖 Documentation Usage Examples

```bash
# For implementing authentication
Reference: settings/docs/authentication.md
Contains: JWT middleware, bcrypt patterns, Redis session management

# For adding real-time features  
Reference: settings/docs/messaging-system.md
Contains: WebSocket hub implementation, client integration patterns

# For UI development
Reference: settings/docs/ui-ux-patterns.md
Contains: Responsive components, theming, glass effects, navigation

# For database operations
Reference: settings/docs/database-api.md
Contains: GORM relationships, repository patterns, caching strategies
```

## AI Agent & Model Integration Guidelines

- **Reference Endpoints and Tables**: Use the provided endpoint patterns and table names for automation, code generation, and data extraction.
- **Module Mapping**: Each feature/module is mapped to endpoints and tables for easy traversal and reference.
- **Environment Awareness**: Respect environment configuration for dev/staging/prod when generating or executing code.
- **Error Handling**: Follow structured error conventions (JSON/logrus) for backend responses.
- **UI/UX Consistency**: Use themed components and routing conventions for frontend automation.
- **Database Relationships**: Leverage provided relationships for entity linking and query generation.
- **Hot Reload**: Use provided scripts for development automation and troubleshooting.
- **Documentation First**: Always reference `settings/docs/` for implementation patterns before code generation.

---

## Getting Started

### Prerequisites

- Docker & Docker Compose
- Node.js 22+ (for local development)
- Go 1.25+ (for local development)

### Quick Start

```bash
# Clone and navigate to project
git clone <repository-url>
cd quester

# Start development environment
./docker.sh dev up

# Access applications
# Client: http://localhost:19000
# Server API: http://localhost:8000
# Database: localhost:5432
```

### Environment Management

```bash
# Switch environments
./env-switch.sh dev     # Development
./env-switch.sh staging # Staging
./env-switch.sh prod    # Production

# Check status
./docker.sh dev status
```

## Development Workflow & Commands

### Environment Management Tasks

```bash
# Environment switching (auto-syncs .env files)
./env-switch.sh dev         # Development environment
./env-switch.sh staging     # Staging environment  
./env-switch.sh prod        # Production environment

# Quick aliases (after sourcing env-aliases.sh)
env-dev                     # Switch to development
env-staging                 # Switch to staging
env-prod                    # Switch to production
```

### Docker Operations

```bash
# Primary development workflow
./docker.sh dev up          # Start all services (auto-switches env)
./docker.sh dev down        # Stop all services
./docker.sh dev status      # Check environment and service status
./docker.sh dev logs        # View combined logs
./docker.sh dev build       # Rebuild all containers

# Individual service management
./docker.sh dev server      # Server logs only
./docker.sh dev client      # Client logs only
./docker.sh dev db          # Database logs only

# Quick aliases (after sourcing docker-aliases.sh)
docker-dev-up               # Start development
dev-server                  # Server development
dev-client                  # Client development
```

### Hot Reload & Development

```bash
# Backend (Go) - Automatic with Air
# Changes to server/ trigger rebuild
# Logs: server/app.log + Docker logs

# Frontend (React Native) - Automatic with Metro
# Changes to client/ trigger refresh
# Access: http://localhost:19000 (Expo DevTools)

# Hot reload troubleshooting
./hot-reload-restart.sh     # Linux/macOS restart script
./hot-reload-restart.ps1    # Windows PowerShell restart script
```

### Database Operations

```bash
# Database access
docker exec -it quester-postgres-dev psql -U postgres -d quester

# Redis access  
docker exec -it quester-redis-dev redis-cli

# Manual migrations (auto-migration on server start)
# Models defined in: server/internal/models/models.go
```

---

## Code Conventions & Standards

#### Go Backend

```go
// Package structure
package main
import "quester/internal/...

// Route patterns
router.Get("/api/v1/users", handlers.GetUsers)
router.Post("/api/v1/auth/login", auth.Login)

// Error handling
if err != nil {
    log.WithError(err).Error("Operation failed")
    return c.Status(500).JSON(fiber.Map{
        "error": "Internal server error",
    })
}
```

#### React Native Frontend

```typescript
// File-based routing
import { useRouter } from 'expo-router';

// Component patterns
import { ThemedText } from '@/components/ThemedText';
import { useColorScheme } from '@/hooks/useColorScheme';

// Platform-specific files
icon-symbol.ios.tsx    // iOS-specific implementation
icon-symbol.tsx        // Default/Android implementation
```

---

## Project Structure Reference

```text
quester/
├── client/                 # React Native Expo frontend
│   ├── app/               # File-based routing (Expo Router)
│   │   ├── (tabs)/        # Tab navigation
│   │   ├── _layout.tsx    # Root layout
│   │   └── modal.tsx      # Modal screens
│   ├── components/        # Reusable UI components
│   │   ├── ui/            # Core UI primitives
│   │   └── themed-*.tsx   # Theme-aware components
│   ├── constants/         # App constants & themes
│   ├── hooks/             # Custom React hooks
│   └── scripts/           # Development utilities
├── server/                # Go backend application
│   ├── internal/          # Private application code
│   │   ├── models/        # Database models (GORM)
│   │   ├── routes/        # API route handlers
│   │   ├── middleware/    # HTTP middleware
│   │   └── handlers/      # Business logic
│   ├── main.go           # Application entry point
│   └── .air.toml         # Hot reload configuration
├── settings/              # Infrastructure & deployment
│   ├── docker-compose.*.yml  # Environment-specific configs
│   ├── *.dockerfile      # Container definitions
│   ├── nginx/            # Reverse proxy configs
│   ├── postgres/         # Database configurations
│   └── redis/            # Cache configurations
└── env-*.{sh,ps1}        # Environment management utilities
```

---

## Deployment & Production

### Backend Development (Go)

- Hot reload with Air
- GORM auto-migration
- Structured logging to `app.log`
- API routes under `/api/v1`

### Frontend Development (React Native)

- Expo Router with file-based routing
- Hot reload and fast refresh
- Themed components for consistency
- TypeScript strict mode

### Database Management

- Auto-migration on server startup
- GORM models with relationships
- Redis for caching and sessions

## Project Structure

```
quester/
├── client/           # React Native Expo frontend
│   ├── app/         # File-based routing
│   └── components/  # Reusable UI components
├── server/          # Go backend
│   ├── internal/    # Application logic
│   └── models/      # Database models
├── settings/        # Docker configurations
└── env-*.{sh,ps1}   # Environment utilities
```

## Contributing

1. Choose appropriate environment: `./env-switch.sh dev`
2. Start services: `./docker.sh dev up`
3. Make changes with hot reload enabled
4. Test across mobile and web platforms
5. Follow established code conventions

## License

[Add your license information here]
