# Server Setup & Implementation Guide

## Project Initialization

### Create Go Module
```bash
mkdir server
cd server
go mod init quester-server
```

### Essential Dependencies
```bash
go get github.com/gin-gonic/gin
go get gorm.io/gorm
go get gorm.io/driver/postgres
go get github.com/golang-jwt/jwt/v5
go get github.com/gorilla/websocket
go get github.com/joho/godotenv
go get golang.org/x/crypto/bcrypt
go get github.com/gin-contrib/cors
```

## Project Structure Setup

### Create Directory Structure
```bash
mkdir -p cmd/server
mkdir -p internal/{config,database,handlers,middleware,models,routes,services,utils,websocket}
mkdir -p migrations
mkdir -p uploads
```

## Core Configuration

### cmd/server/main.go
```go
package main

import (
    "log"
    "os"

    "quester-server/internal/config"
    "quester-server/internal/database"
    "quester-server/internal/routes"
    "quester-server/internal/websocket"

    "github.com/gin-gonic/gin"
    "github.com/joho/godotenv"
)

func main() {
    // Load environment variables
    if err := godotenv.Load(); err != nil {
        log.Println("No .env file found")
    }

    // Load configuration
    cfg := config.LoadConfig()

    // Initialize database
    db, err := database.InitDB(cfg.DatabaseURL)
    if err != nil {
        log.Fatal("Failed to connect to database:", err)
    }

    // Initialize WebSocket hub
    hub := websocket.NewHub()
    go hub.Run()

    // Initialize Gin router
    router := gin.Default()

    // Setup routes
    routes.SetupRoutes(router, db, hub)

    // Start server
    port := cfg.Port
    if port == "" {
        port = "8080"
    }

    log.Printf("Server starting on port %s", port)
    log.Fatal(router.Run(":" + port))
}
```

### internal/config/config.go
```go
package config

import "os"

type Config struct {
    Port            string
    DatabaseURL     string
    RedisURL        string
    JWTSecret       string
    JWTRefreshSecret string
    AllowedOrigins  []string
    UploadPath      string
    MaxFileSize     int64
}

func LoadConfig() *Config {
    return &Config{
        Port:             getEnv("PORT", "8080"),
        DatabaseURL:      getEnv("DATABASE_URL", ""),
        RedisURL:         getEnv("REDIS_URL", "redis://localhost:6379"),
        JWTSecret:        getEnv("JWT_SECRET", ""),
        JWTRefreshSecret: getEnv("JWT_REFRESH_SECRET", ""),
        UploadPath:       getEnv("UPLOAD_PATH", "./uploads"),
        MaxFileSize:      parseInt64(getEnv("MAX_FILE_SIZE", "10485760")), // 10MB
    }
}

func getEnv(key, defaultValue string) string {
    if value := os.Getenv(key); value != "" {
        return value
    }
    return defaultValue
}

func parseInt64(str string) int64 {
    // Implementation for parsing int64
    return 10485760 // Default 10MB
}
```

## Database Setup

### internal/database/database.go
```go
package database

import (
    "gorm.io/driver/postgres"
    "gorm.io/gorm"
    "gorm.io/gorm/logger"
)

func InitDB(databaseURL string) (*gorm.DB, error) {
    db, err := gorm.Open(postgres.Open(databaseURL), &gorm.Config{
        Logger: logger.Default.LogMode(logger.Info),
    })
    if err != nil {
        return nil, err
    }

    // Run migrations
    if err := runMigrations(db); err != nil {
        return nil, err
    }

    return db, nil
}

func runMigrations(db *gorm.DB) error {
    return db.AutoMigrate(
        &models.User{},
        &models.Quest{},
        &models.Task{},
        &models.Message{},
        &models.Notification{},
    )
}
```

## Core Models

### internal/models/base.go
```go
package models

import (
    "time"
    "gorm.io/gorm"
)

type BaseModel struct {
    ID        uint           `json:"id" gorm:"primaryKey"`
    CreatedAt time.Time      `json:"created_at"`
    UpdatedAt time.Time      `json:"updated_at"`
    DeletedAt gorm.DeletedAt `json:"-" gorm:"index"`
}
```

### internal/models/user.go
```go
package models

type User struct {
    BaseModel
    Email        string `json:"email" gorm:"uniqueIndex;not null"`
    Username     string `json:"username" gorm:"uniqueIndex;not null"`
    FirstName    string `json:"first_name" gorm:"not null"`
    LastName     string `json:"last_name" gorm:"not null"`
    PasswordHash string `json:"-" gorm:"not null"`
    Avatar       string `json:"avatar"`
    Level        int    `json:"level" gorm:"default:1"`
    TotalPoints  int    `json:"total_points" gorm:"default:0"`
    IsVerified   bool   `json:"is_verified" gorm:"default:false"`
    IsActive     bool   `json:"is_active" gorm:"default:true"`
    Role         string `json:"role" gorm:"default:user"`
}

type LoginRequest struct {
    Email    string `json:"email" binding:"required,email"`
    Password string `json:"password" binding:"required"`
}

type RegisterRequest struct {
    Email     string `json:"email" binding:"required,email"`
    Username  string `json:"username" binding:"required"`
    FirstName string `json:"first_name" binding:"required"`
    LastName  string `json:"last_name" binding:"required"`
    Password  string `json:"password" binding:"required,min=8"`
}

type AuthResponse struct {
    User         User   `json:"user"`
    AccessToken  string `json:"access_token"`
    RefreshToken string `json:"refresh_token"`
}
```

### internal/models/quest.go
```go
package models

import "time"

type Quest struct {
    BaseModel
    Title       string     `json:"title" gorm:"not null"`
    Description string     `json:"description"`
    Difficulty  string     `json:"difficulty" gorm:"default:medium"`
    Priority    string     `json:"priority" gorm:"default:medium"`
    Status      string     `json:"status" gorm:"default:pending"`
    Points      int        `json:"points" gorm:"default:0"`
    CreatedByID uint       `json:"created_by_id"`
    CreatedBy   User       `json:"created_by" gorm:"foreignKey:CreatedByID"`
    AssignedToID *uint     `json:"assigned_to_id"`
    AssignedTo  *User      `json:"assigned_to" gorm:"foreignKey:AssignedToID"`
    DueDate     *time.Time `json:"due_date"`
    Tasks       []Task     `json:"tasks" gorm:"foreignKey:QuestID"`
}

type Task struct {
    BaseModel
    QuestID      uint       `json:"quest_id"`
    Quest        Quest      `json:"quest" gorm:"foreignKey:QuestID"`
    Title        string     `json:"title" gorm:"not null"`
    Description  string     `json:"description"`
    Status       string     `json:"status" gorm:"default:pending"`
    Priority     string     `json:"priority" gorm:"default:medium"`
    Points       int        `json:"points" gorm:"default:0"`
    AssignedToID *uint      `json:"assigned_to_id"`
    AssignedTo   *User      `json:"assigned_to" gorm:"foreignKey:AssignedToID"`
    DueDate      *time.Time `json:"due_date"`
}
```

## JWT Authentication

### internal/utils/jwt.go
```go
package utils

import (
    "errors"
    "os"
    "time"

    "github.com/golang-jwt/jwt/v5"
)

type Claims struct {
    UserID uint   `json:"user_id"`
    Email  string `json:"email"`
    Role   string `json:"role"`
    jwt.RegisteredClaims
}

func GenerateToken(userID uint, email, role string) (string, error) {
    claims := Claims{
        UserID: userID,
        Email:  email,
        Role:   role,
        RegisteredClaims: jwt.RegisteredClaims{
            ExpiresAt: jwt.NewNumericDate(time.Now().Add(24 * time.Hour)),
            IssuedAt:  jwt.NewNumericDate(time.Now()),
        },
    }

    token := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)
    return token.SignedString([]byte(os.Getenv("JWT_SECRET")))
}

func GenerateRefreshToken(userID uint) (string, error) {
    claims := jwt.RegisteredClaims{
        Subject:   fmt.Sprintf("%d", userID),
        ExpiresAt: jwt.NewNumericDate(time.Now().Add(7 * 24 * time.Hour)), // 7 days
        IssuedAt:  jwt.NewNumericDate(time.Now()),
    }

    token := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)
    return token.SignedString([]byte(os.Getenv("JWT_REFRESH_SECRET")))
}

func ValidateToken(tokenString string) (*Claims, error) {
    token, err := jwt.ParseWithClaims(tokenString, &Claims{}, func(token *jwt.Token) (interface{}, error) {
        return []byte(os.Getenv("JWT_SECRET")), nil
    })

    if err != nil {
        return nil, err
    }

    if claims, ok := token.Claims.(*Claims); ok && token.Valid {
        return claims, nil
    }

    return nil, errors.New("invalid token")
}
```

### internal/middleware/auth.go
```go
package middleware

import (
    "net/http"
    "strings"

    "quester-server/internal/utils"

    "github.com/gin-gonic/gin"
)

func AuthMiddleware() gin.HandlerFunc {
    return func(c *gin.Context) {
        authHeader := c.GetHeader("Authorization")
        if authHeader == "" {
            c.JSON(http.StatusUnauthorized, gin.H{"error": "Authorization header required"})
            c.Abort()
            return
        }

        tokenString := strings.TrimPrefix(authHeader, "Bearer ")
        claims, err := utils.ValidateToken(tokenString)
        if err != nil {
            c.JSON(http.StatusUnauthorized, gin.H{"error": "Invalid token"})
            c.Abort()
            return
        }

        c.Set("user_id", claims.UserID)
        c.Set("user_email", claims.Email)
        c.Set("user_role", claims.Role)
        c.Next()
    }
}
```

## Authentication Handler

### internal/handlers/auth.go
```go
package handlers

import (
    "net/http"
    "strconv"

    "quester-server/internal/models"
    "quester-server/internal/services"

    "github.com/gin-gonic/gin"
    "gorm.io/gorm"
)

type AuthHandler struct {
    authService *services.AuthService
}

func NewAuthHandler(db *gorm.DB) *AuthHandler {
    return &AuthHandler{
        authService: services.NewAuthService(db),
    }
}

func (h *AuthHandler) Register(c *gin.Context) {
    var req models.RegisterRequest
    if err := c.ShouldBindJSON(&req); err != nil {
        c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
        return
    }

    response, err := h.authService.Register(req)
    if err != nil {
        c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
        return
    }

    c.JSON(http.StatusCreated, response)
}

func (h *AuthHandler) Login(c *gin.Context) {
    var req models.LoginRequest
    if err := c.ShouldBindJSON(&req); err != nil {
        c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
        return
    }

    response, err := h.authService.Login(req.Email, req.Password)
    if err != nil {
        c.JSON(http.StatusUnauthorized, gin.H{"error": err.Error()})
        return
    }

    c.JSON(http.StatusOK, response)
}

func (h *AuthHandler) GetMe(c *gin.Context) {
    userID, exists := c.Get("user_id")
    if !exists {
        c.JSON(http.StatusUnauthorized, gin.H{"error": "User ID not found"})
        return
    }

    user, err := h.authService.GetUserByID(userID.(uint))
    if err != nil {
        c.JSON(http.StatusNotFound, gin.H{"error": "User not found"})
        return
    }

    c.JSON(http.StatusOK, user)
}
```

## Routes Setup

### internal/routes/routes.go
```go
package routes

import (
    "quester-server/internal/handlers"
    "quester-server/internal/middleware"
    "quester-server/internal/websocket"

    "github.com/gin-gonic/gin"
    "gorm.io/gorm"
)

func SetupRoutes(router *gin.Gin, db *gorm.DB, hub *websocket.Hub) {
    // Middleware
    router.Use(middleware.CORSMiddleware())
    router.Use(middleware.LoggingMiddleware())

    // Initialize handlers
    authHandler := handlers.NewAuthHandler(db)
    userHandler := handlers.NewUserHandler(db)
    questHandler := handlers.NewQuestHandler(db)
    messageHandler := handlers.NewMessageHandler(db)
    wsHandler := handlers.NewWebSocketHandler(hub)

    // API routes
    api := router.Group("/api/v1")
    {
        // Public routes
        auth := api.Group("/auth")
        {
            auth.POST("/register", authHandler.Register)
            auth.POST("/login", authHandler.Login)
            auth.POST("/refresh", authHandler.RefreshToken)
        }

        // Protected routes
        protected := api.Group("/")
        protected.Use(middleware.AuthMiddleware())
        {
            // Auth
            protected.GET("/auth/me", authHandler.GetMe)

            // Users
            protected.GET("/users", userHandler.GetUsers)
            protected.GET("/users/:id", userHandler.GetUser)
            protected.PUT("/users/:id", userHandler.UpdateUser)

            // Quests
            protected.GET("/quests", questHandler.GetQuests)
            protected.POST("/quests", questHandler.CreateQuest)
            protected.GET("/quests/:id", questHandler.GetQuest)
            protected.PUT("/quests/:id", questHandler.UpdateQuest)
            protected.DELETE("/quests/:id", questHandler.DeleteQuest)

            // Tasks
            protected.GET("/tasks", questHandler.GetTasks)
            protected.POST("/tasks", questHandler.CreateTask)
            protected.PUT("/tasks/:id", questHandler.UpdateTask)
            protected.DELETE("/tasks/:id", questHandler.DeleteTask)

            // Messages
            protected.GET("/messages", messageHandler.GetMessages)
            protected.POST("/messages", messageHandler.SendMessage)
            protected.PUT("/messages/:id/read", messageHandler.MarkAsRead)
        }
    }

    // WebSocket endpoint
    router.GET("/ws", wsHandler.HandleWebSocket)
}
```

## Environment Configuration

### .env
```bash
# Server Configuration
PORT=8080

# Database
DATABASE_URL=postgresql://username:password@localhost:5432/quester

# JWT Secrets
JWT_SECRET=your-super-secret-jwt-key-here-min-32-chars
JWT_REFRESH_SECRET=your-refresh-secret-here-min-32-chars

# Redis (optional)
REDIS_URL=redis://localhost:6379

# File Uploads
UPLOAD_PATH=./uploads
MAX_FILE_SIZE=10485760

# CORS
ALLOWED_ORIGINS=http://localhost:3000,http://localhost:8081
```

## Quick Setup Checklist

### 1. Project Structure
- [ ] Initialize Go module
- [ ] Install dependencies
- [ ] Create directory structure

### 2. Database
- [ ] Set up PostgreSQL database
- [ ] Configure database connection
- [ ] Implement models and migrations

### 3. Authentication
- [ ] Implement JWT utilities
- [ ] Create auth middleware
- [ ] Build auth handlers

### 4. API Structure
- [ ] Set up routes and handlers
- [ ] Implement CORS middleware
- [ ] Add request logging

### 5. Environment
- [ ] Configure .env file
- [ ] Set up development workflow

### 6. Testing
- [ ] Test authentication endpoints
- [ ] Verify CORS configuration
- [ ] Test database connectivity

## Development Commands

```bash
# Run development server
go run cmd/server/main.go

# Build for production
go build -o quester-server cmd/server/main.go

# Run with environment variables
go run cmd/server/main.go

# Install dependencies
go mod tidy
```

This setup provides a robust Go server foundation with JWT authentication, database integration, and API structure ready for the Quester application.