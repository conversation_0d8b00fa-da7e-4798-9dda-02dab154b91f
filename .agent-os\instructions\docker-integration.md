# Docker Integration Instructions

## Development Environment Setup

### Environment Management
The project uses environment-specific Docker Compose files:
- `docker-compose.dev.yml` - Development environment
- `docker-compose.staging.yml` - Staging environment
- `docker-compose.prod.yml` - Production environment

### Environment Switching
```bash
# Switch environments using the env-switch script
./env-switch.sh dev         # Development
./env-switch.sh staging     # Staging
./env-switch.sh prod        # Production

# Docker operations
./docker.sh dev up          # Start development environment
./docker.sh dev down        # Stop all services
./docker.sh dev status      # Check service status
./docker.sh dev logs        # View combined logs
./docker.sh dev build       # Rebuild containers
```

## Service Architecture

### Development Services
```yaml
services:
  # PostgreSQL Database
  postgres:
    image: postgres:15
    environment:
      POSTGRES_DB: quester
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: password
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data

  # Redis Cache
  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data

  # Go Backend Server
  server:
    build:
      context: .
      dockerfile: settings/server.dockerfile
    ports:
      - "8000:8000"
    environment:
      - DB_HOST=postgres
      - REDIS_HOST=redis
      - JWT_SECRET=your-secret-key
    depends_on:
      - postgres
      - redis
    volumes:
      - ./server:/app
      - /app/tmp  # Air build cache

  # React Native Client (Development)
  client:
    build:
      context: .
      dockerfile: settings/client.dockerfile
    ports:
      - "19000:19000"  # Expo Dev Tools
      - "19001:19001"  # Expo Dev Server
      - "19002:19002"  # Metro Bundler
    volumes:
      - ./client:/app
      - /app/node_modules

  # MailHog (Email Testing)
  mailhog:
    image: mailhog/mailhog
    ports:
      - "1025:1025"  # SMTP
      - "8025:8025"  # Web Interface
```

## Container Configurations

### Go Server Dockerfile
```dockerfile
# settings/server.dockerfile
FROM golang:1.21-alpine AS builder

WORKDIR /app

# Install Air for hot reload
RUN go install github.com/cosmtrek/air@latest

# Copy go mod files
COPY server/go.mod server/go.sum ./
RUN go mod download

# Copy source code
COPY server/ .

# Development stage
FROM golang:1.21-alpine AS development

WORKDIR /app

# Install Air for hot reload
RUN go install github.com/cosmtrek/air@latest

# Copy source code
COPY --from=builder /app .

EXPOSE 8000

# Use Air for hot reload in development
CMD ["air", "-c", ".air.toml"]

# Production build stage
FROM builder AS production-builder

# Build the application
RUN CGO_ENABLED=0 GOOS=linux go build -o main ./cmd/server

# Production runtime stage
FROM alpine:latest AS production

RUN apk --no-cache add ca-certificates
WORKDIR /root/

# Copy the binary from builder stage
COPY --from=production-builder /app/main .

EXPOSE 8000

CMD ["./main"]
```

### React Native Client Dockerfile
```dockerfile
# settings/client.dockerfile
FROM node:18-alpine

WORKDIR /app

# Copy package files
COPY client/package*.json ./
RUN npm install

# Copy source code
COPY client/ .

EXPOSE 19000 19001 19002

# Start Expo development server
CMD ["npx", "expo", "start", "--host", "0.0.0.0"]
```

## Hot Reload Configuration

### Air Configuration for Go (.air.toml)
```toml
root = "."
testdata_dir = "testdata"
tmp_dir = "tmp"

[build]
  args_bin = []
  bin = "./tmp/main"
  cmd = "go build -o ./tmp/main ./cmd/server"
  delay = 1000
  exclude_dir = ["assets", "tmp", "vendor", "testdata"]
  exclude_file = []
  exclude_regex = ["_test.go"]
  exclude_unchanged = false
  follow_symlink = false
  full_bin = ""
  include_dir = []
  include_ext = ["go", "tpl", "tmpl", "html"]
  kill_delay = "0s"
  log = "build-errors.log"
  send_interrupt = false
  stop_on_root = false

[color]
  app = ""
  build = "yellow"
  main = "magenta"
  runner = "green"
  watcher = "cyan"

[log]
  time = false

[misc]
  clean_on_exit = false
```

### Expo Configuration for React Native
```json
{
  "expo": {
    "name": "Quester Client",
    "slug": "quester-client",
    "version": "1.0.0",
    "orientation": "portrait",
    "icon": "./assets/icon.png",
    "userInterfaceStyle": "automatic",
    "splash": {
      "image": "./assets/splash.png",
      "resizeMode": "contain",
      "backgroundColor": "#ffffff"
    },
    "assetBundlePatterns": [
      "**/*"
    ],
    "ios": {
      "supportsTablet": true
    },
    "android": {
      "adaptiveIcon": {
        "foregroundImage": "./assets/adaptive-icon.png",
        "backgroundColor": "#ffffff"
      }
    },
    "web": {
      "bundler": "metro",
      "output": "static",
      "favicon": "./assets/favicon.png"
    },
    "plugins": [
      "expo-router"
    ],
    "experiments": {
      "typedRoutes": true
    }
  }
}
```

## Production Deployment

### Nginx Configuration
```nginx
# settings/nginx/nginx.conf
events {
    worker_connections 1024;
}

http {
    upstream backend {
        server server:8000;
    }

    upstream frontend {
        server client:19000;
    }

    server {
        listen 80;
        server_name localhost;

        # API requests
        location /api/ {
            proxy_pass http://backend;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }

        # WebSocket connections
        location /ws/ {
            proxy_pass http://backend;
            proxy_http_version 1.1;
            proxy_set_header Upgrade $http_upgrade;
            proxy_set_header Connection "upgrade";
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }

        # Frontend requests
        location / {
            proxy_pass http://frontend;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }
    }
}
```

## Environment Variables

### Development Environment (.env.dev)
```env
# Database
DB_HOST=postgres
DB_PORT=5432
DB_USER=postgres
DB_PASSWORD=password
DB_NAME=quester

# Redis
REDIS_HOST=redis
REDIS_PORT=6379
REDIS_PASSWORD=

# JWT
JWT_SECRET=your-development-secret-key-here

# Server
PORT=8000
GIN_MODE=debug

# SMTP (MailHog)
SMTP_HOST=mailhog
SMTP_PORT=1025
SMTP_USER=
SMTP_PASSWORD=

# Client
EXPO_DEV_SERVER_URL=http://localhost:19000
API_URL=http://localhost:8000
```

### Production Environment (.env.prod)
```env
# Database
DB_HOST=postgres-prod
DB_PORT=5432
DB_USER=postgres
DB_PASSWORD=secure-production-password
DB_NAME=quester_prod

# Redis
REDIS_HOST=redis-prod
REDIS_PORT=6379
REDIS_PASSWORD=secure-redis-password

# JWT
JWT_SECRET=secure-production-jwt-secret-key

# Server
PORT=8000
GIN_MODE=release

# SMTP
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASSWORD=your-app-password

# SSL/TLS
SSL_CERT_PATH=/etc/ssl/certs/cert.pem
SSL_KEY_PATH=/etc/ssl/private/key.pem
```

## Volume Management

### Data Persistence
```yaml
volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local
  server_logs:
    driver: local
```

### Backup Scripts
```bash
#!/bin/bash
# backup.sh

DATE=$(date +%Y%m%d_%H%M%S)

# Backup PostgreSQL
docker exec quester-postgres-prod pg_dump -U postgres quester > "backup_${DATE}.sql"

# Backup Redis
docker exec quester-redis-prod redis-cli BGSAVE

# Compress backups
tar -czf "quester_backup_${DATE}.tar.gz" backup_${DATE}.sql

echo "Backup completed: quester_backup_${DATE}.tar.gz"
```

## Health Checks

### Docker Health Checks
```yaml
services:
  server:
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  postgres:
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres"]
      interval: 10s
      timeout: 5s
      retries: 5

  redis:
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5
```

## Monitoring and Logging

### Log Configuration
```yaml
services:
  server:
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"

  client:
    logging:
      driver: "json-file"
      options:
        max-size: "5m"
        max-file: "3"
```

### Log Aggregation
```bash
# View all logs
./docker.sh dev logs

# View specific service logs
docker-compose -f settings/docker-compose.dev.yml logs server
docker-compose -f settings/docker-compose.dev.yml logs client
docker-compose -f settings/docker-compose.dev.yml logs postgres
```