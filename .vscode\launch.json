{
    // Use IntelliSense to learn about possible attributes.
    // Hover to view descriptions of existing attributes.
    // For more information, visit: https://go.microsoft.com/fwlink/?linkid=830387
    "version": "0.2.0",
    "configurations": [

        {
            "name": "Debug Android",
            "cwd": "${workspaceFolder}/client",
            "type": "reactnative",
            "request": "launch",
            "platform": "android"
        },
        {
            "name": "Run Android",
            "cwd": "${workspaceFolder}/client",
            "type": "reactnative",
            "request": "launch",
            "platform": "android",
            "enableDebug": false
        },
        {
            "name": "Debug iOS",
            "cwd": "${workspaceFolder}/client",
            "type": "reactnative",
            "request": "launch",
            "platform": "ios"
        },
        {
            "name": "Run iOS",
            "cwd": "${workspaceFolder}/client",
            "type": "reactnative",
            "request": "launch",
            "platform": "ios",
            "enableDebug": false
        },
        {
            "name": "Debug Windows",
            "cwd": "${workspaceFolder}/client",
            "type": "reactnative",
            "request": "launch",
            "platform": "windows"
        },
        {
            "name": "Debug macOS",
            "cwd": "${workspaceFolder}/client",
            "type": "reactnative",
            "request": "launch",
            "platform": "macos"
        },
        {
            "name": "Attach to packager",
            "cwd": "${workspaceFolder}/client",
            "type": "reactnative",
            "request": "attach"
        }
    ]
}