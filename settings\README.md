# Docker Setup for Quester - Golang Server & React Native Expo Client

This directory contains Docker configurations for a Golang server and React Native Expo client across development, staging, and production environments.

## Architecture

- **Server**: Golang with Gin/Echo framework
- **Client**: React Native Expo (with web build support)
- **Database**: PostgreSQL
- **Cache**: Redis
- **Proxy**: <PERSON>in<PERSON> (staging/production)

## Quick Start

### Development
```bash
cd settings
chmod +x deploy.sh
./deploy.sh dev up
```

### Staging
```bash
./deploy.sh staging up
```

### Production
```bash
./deploy.sh prod up
```

## Environment Files

- `.env.dev` - Development environment variables
- `.env.staging` - Staging environment variables  
- `.env.prod` - Production environment variables

**Important**: Update production passwords and secrets before deploying!

### Environment Management

This project includes an advanced environment management system with automatic synchronization between the settings directory and your server/client applications.

**Quick Environment Switching:**
```bash
# From the root directory
./env-switch.sh dev      # Switch to development
./env-switch.sh staging  # Switch to staging  
./env-switch.sh prod     # Switch to production
```

**Advanced Management:**
```bash
./env-manager.ps1 status  # Check current environment and file status
./env-manager.ps1 sync    # Sync current environment files
./env-manager.ps1 watch   # Auto-watch for changes and sync
```

**Load Convenient Aliases:**
```bash
source env-aliases.sh     # Load shortcuts like env-dev, env-staging, etc.
```

See [ENV-README.md](../ENV-README.md) for detailed environment management documentation.

## Services

### Development
- **server**: Golang API server with hot reload (Air)
- **client**: Expo development server with tunnel
- **postgres**: PostgreSQL database
- **redis**: Redis cache
- **mailhog**: Email testing

Ports:
- Server API: `http://localhost:8000`
- Expo DevTools: `http://localhost:19000`
- Metro Bundler: `http://localhost:8081`
- Database: `localhost:5432`
- Redis: `localhost:6379`
- MailHog: `http://localhost:8025`

### Staging/Production
- **server**: Golang API server (production build)
- **client**: Expo web build served by Nginx
- **postgres**: PostgreSQL with custom configuration
- **redis**: Redis with custom configuration
- **nginx**: Reverse proxy with SSL termination
- **backup**: Automated database backups (production only)

## Commands

```bash
# Start full environment
./deploy.sh <env> up

# Start only server services
./deploy.sh <env> server

# Start only client service  
./deploy.sh <env> client

# Stop environment
./deploy.sh <env> down

# Restart environment
./deploy.sh <env> restart

# View logs
./deploy.sh <env> logs

# Rebuild images
./deploy.sh <env> build
```

## Golang Server Setup

The server expects this structure:
```
server/
├── cmd/
│   └── server/
│       └── main.go
├── go.mod
├── go.sum
├── configs/
├── migrations/
└── .air.toml (copied from settings)
```

## React Native Expo Client Setup

The client expects this structure:
```
client/
├── package.json
├── app.json
├── App.js/App.tsx
└── expo configuration files
```

### Mobile Development

For mobile development, use Expo CLI:
```bash
# In development environment
docker-compose -f docker-compose.dev.yml exec client bash
npx expo start --tunnel
```

### Web Build

For web deployment (staging/production), the client builds to static files served by Nginx.

## SSL Certificates

For staging and production, place SSL certificates in `ssl/` directory:
- `cert.pem` - SSL certificate
- `key.pem` - SSL private key

## Environment URLs

- **Development**: 
  - API: `http://localhost:8000`
  - Expo: `http://localhost:19000`
- **Staging**: 
  - Web: `http://staging.quester.com`
  - API: `http://staging.quester.com/api`
- **Production**: 
  - Web: `https://quester.com`
  - API: `https://api.quester.com`

## Hot Reload

- **Server**: Uses Air for automatic Golang compilation and reload
- **Client**: Uses Expo's built-in hot reload and fast refresh

## Database Migrations

Place migration files in `server/migrations/` and they'll be available in the container.

## Backup

Production includes automated PostgreSQL backups every 6 hours, stored in `backups/` with 7-day retention.