{"name": "quester-agent-os", "version": "1.0.0", "description": "Agent OS configuration for Quester - Full-stack React Native + Go application", "project": {"name": "<PERSON><PERSON>", "type": "full-stack", "architecture": "microservices-ready-monolith", "deployment": "containerized"}, "tech_stack": {"frontend": {"framework": "React Native Expo", "language": "TypeScript", "routing": "File-based (Expo Router)", "state_management": "React hooks + Context API", "ui_framework": "Custom themed components", "platform_support": ["iOS", "Android", "Web"]}, "backend": {"framework": "Go + Fiber", "orm": "GORM", "authentication": "JWT tokens", "realtime": "WebSocket", "api_pattern": "RESTful with /api/v1 prefix", "logging": "Structured JSON (logrus)"}, "database": {"primary": "PostgreSQL", "cache": "Redis", "containerization": "Docker + <PERSON>er Compose"}, "infrastructure": {"reverse_proxy": "<PERSON><PERSON><PERSON>", "hot_reload": {"backend": "Air (Go)", "frontend": "Metro (React Native)"}}}, "environments": {"development": {"postgres": "localhost:5432", "redis": "localhost:6379", "server": "localhost:8000", "client": "localhost:19000", "mailhog": "localhost:8025"}, "staging": {"nginx_proxy": true, "automated_backups": true}, "production": {"nginx_proxy": true, "automated_backups": true, "multi_replica_deployment": true}}, "agents": {"code_generation": {"enabled": true, "templates": ["react-native", "go-fiber", "docker"]}, "testing": {"enabled": true, "frameworks": ["jest", "go-test"]}, "deployment": {"enabled": true, "platforms": ["docker", "kubernetes"]}}, "workflows": {"development": ["environment_setup", "code_generation", "testing", "hot_reload"], "deployment": ["build", "test", "containerize", "deploy"]}}