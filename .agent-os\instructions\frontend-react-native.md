# React Native Frontend Instructions

## Expo Router File-Based Routing

### Directory Structure
```
client/app/
├── (tabs)/                 # Tab navigation
│   ├── index.tsx          # Home tab
│   ├── explore.tsx        # Explore tab
│   └── _layout.tsx        # Tab layout
├── _layout.tsx            # Root layout
├── modal.tsx              # Modal screens
└── +not-found.tsx         # 404 page
```

### Component Patterns

#### Themed Components
```typescript
import { ThemedText } from '@/components/ThemedText';
import { ThemedView } from '@/components/ThemedView';
import { useColorScheme } from '@/hooks/useColorScheme';

export default function ExampleScreen() {
  const colorScheme = useColorScheme();

  return (
    <ThemedView style={styles.container}>
      <ThemedText type="title">Welcome to Quester</ThemedText>
    </ThemedView>
  );
}
```

#### Platform-Specific Components
```typescript
// icon-symbol.ios.tsx (iOS-specific)
import { SymbolView } from 'expo-symbols';

// icon-symbol.tsx (Default/Android)
import Ionicons from '@expo/vector-icons/Ionicons';
```

### State Management Patterns

#### Context API Usage
```typescript
import { createContext, useContext, useReducer } from 'react';

interface AppState {
  user: User | null;
  theme: 'light' | 'dark';
}

const AppContext = createContext<AppState | null>(null);

export function AppProvider({ children }: { children: React.ReactNode }) {
  const [state, dispatch] = useReducer(appReducer, initialState);

  return (
    <AppContext.Provider value={{ state, dispatch }}>
      {children}
    </AppContext.Provider>
  );
}
```

### API Integration Patterns

#### Fetch with Error Handling
```typescript
const API_BASE = 'http://localhost:8000/api/v1';

export async function apiRequest<T>(
  endpoint: string,
  options?: RequestInit
): Promise<T> {
  try {
    const response = await fetch(`${API_BASE}${endpoint}`, {
      headers: {
        'Content-Type': 'application/json',
        ...options?.headers,
      },
      ...options,
    });

    if (!response.ok) {
      throw new Error(`API Error: ${response.status}`);
    }

    return await response.json();
  } catch (error) {
    console.error('API request failed:', error);
    throw error;
  }
}
```

#### WebSocket Integration
```typescript
import { useEffect, useState } from 'react';

export function useWebSocket(url: string) {
  const [ws, setWs] = useState<WebSocket | null>(null);
  const [messages, setMessages] = useState<any[]>([]);

  useEffect(() => {
    const websocket = new WebSocket(url);

    websocket.onmessage = (event) => {
      const data = JSON.parse(event.data);
      setMessages(prev => [...prev, data]);
    };

    setWs(websocket);

    return () => websocket.close();
  }, [url]);

  return { ws, messages };
}
```

### Navigation Patterns

#### Programmatic Navigation
```typescript
import { useRouter } from 'expo-router';

export default function NavigationExample() {
  const router = useRouter();

  const handleNavigate = () => {
    router.push('/profile');
    // or router.replace('/login');
  };

  return (
    <TouchableOpacity onPress={handleNavigate}>
      <ThemedText>Navigate to Profile</ThemedText>
    </TouchableOpacity>
  );
}
```

### Form Handling

#### Controlled Components
```typescript
import { useState } from 'react';
import { TextInput, Alert } from 'react-native';

export default function LoginForm() {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');

  const handleSubmit = async () => {
    try {
      const response = await apiRequest('/auth/login', {
        method: 'POST',
        body: JSON.stringify({ email, password }),
      });

      // Handle successful login
    } catch (error) {
      Alert.alert('Login Failed', 'Invalid credentials');
    }
  };

  return (
    <ThemedView>
      <TextInput
        value={email}
        onChangeText={setEmail}
        placeholder="Email"
        keyboardType="email-address"
      />
      <TextInput
        value={password}
        onChangeText={setPassword}
        placeholder="Password"
        secureTextEntry
      />
      <TouchableOpacity onPress={handleSubmit}>
        <ThemedText>Login</ThemedText>
      </TouchableOpacity>
    </ThemedView>
  );
}
```

### Styling Patterns

#### Responsive Design
```typescript
import { StyleSheet, Dimensions } from 'react-native';

const { width, height } = Dimensions.get('window');

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: width > 768 ? 40 : 20, // Tablet vs mobile
  },
  grid: {
    flexDirection: width > 768 ? 'row' : 'column',
    flexWrap: 'wrap',
  },
});
```

#### Theme Integration
```typescript
import { Colors } from '@/constants/Colors';
import { useColorScheme } from '@/hooks/useColorScheme';

export default function ThemedComponent() {
  const colorScheme = useColorScheme();
  const colors = Colors[colorScheme ?? 'light'];

  return (
    <View style={[styles.container, { backgroundColor: colors.background }]}>
      <Text style={[styles.text, { color: colors.text }]}>
        Themed content
      </Text>
    </View>
  );
}
```

### Performance Optimization

#### Memoization
```typescript
import { memo, useMemo, useCallback } from 'react';

const ExpensiveComponent = memo(({ data, onPress }) => {
  const processedData = useMemo(() => {
    return data.map(item => ({ ...item, processed: true }));
  }, [data]);

  const handlePress = useCallback((id: string) => {
    onPress(id);
  }, [onPress]);

  return (
    <FlatList
      data={processedData}
      keyExtractor={item => item.id}
      renderItem={({ item }) => (
        <TouchableOpacity onPress={() => handlePress(item.id)}>
          <ThemedText>{item.name}</ThemedText>
        </TouchableOpacity>
      )}
    />
  );
});
```

### Error Boundaries

#### Error Handling Component
```typescript
import { Component, ErrorInfo, ReactNode } from 'react';

interface Props {
  children: ReactNode;
}

interface State {
  hasError: boolean;
}

export class ErrorBoundary extends Component<Props, State> {
  constructor(props: Props) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(): State {
    return { hasError: true };
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    console.error('Error caught by boundary:', error, errorInfo);
  }

  render() {
    if (this.state.hasError) {
      return (
        <ThemedView style={styles.errorContainer}>
          <ThemedText type="title">Something went wrong</ThemedText>
        </ThemedView>
      );
    }

    return this.props.children;
  }
}
```

### Testing Patterns

#### Component Testing
```typescript
import { render, fireEvent } from '@testing-library/react-native';
import LoginForm from '../LoginForm';

describe('LoginForm', () => {
  it('handles login submission', async () => {
    const { getByPlaceholderText, getByText } = render(<LoginForm />);

    fireEvent.changeText(getByPlaceholderText('Email'), '<EMAIL>');
    fireEvent.changeText(getByPlaceholderText('Password'), 'password');
    fireEvent.press(getByText('Login'));

    // Assert expected behavior
  });
});
```