# Quester Project Regeneration Index

## Quick Start Guide for AI

This index provides the optimal order for reading documentation when regenerating the Quester project.

## 🎯 Core System Understanding (Read First)
1. `01-system-overview.md` - High-level architecture and technology stack
2. `02-authentication-flow.md` - Critical authentication implementation
3. `03-project-structure.md` - File organization and conventions

## 🛠 Implementation Guides (Read Second)
4. `04-client-setup.md` - React Native client implementation
5. `05-server-setup.md` - Go server implementation
6. `06-database-schema.md` - Database structure and models

## 🎨 UI Implementation (Read Third)
7. `07-design-system.md` - Color, typography, component standards
8. `08-component-library.md` - Reusable UI components
9. `09-screen-layouts.md` - Page-specific implementations

## 🔧 Advanced Features (Read Last)
10. `10-api-endpoints.md` - Complete API reference
11. `11-websocket-implementation.md` - Real-time features
12. `12-deployment-guide.md` - Production deployment

## 🚀 Quick Regeneration Commands

### Minimum Viable Product (MVP)
```bash
# 1. Client setup
cd client && npm install && npm start

# 2. Server setup
cd server && go mod download && go run cmd/server/main.go
```

### Critical Environment Variables
```
# Client (.env)
EXPO_PUBLIC_API_URL=http://localhost:8080/api/v1

# Server (.env)
DATABASE_URL=postgresql://user:pass@localhost/quester
JWT_SECRET=your-secret-key
```

## 📋 Regeneration Checklist

### Phase 1: Foundation
- [ ] Set up project structure
- [ ] Configure environment variables
- [ ] Initialize database schema
- [ ] Set up authentication system

### Phase 2: Core Features
- [ ] Implement Redux store
- [ ] Create authentication flow
- [ ] Build basic UI components
- [ ] Set up API integration

### Phase 3: Advanced Features
- [ ] Implement WebSocket messaging
- [ ] Add quest/task management
- [ ] Create responsive design
- [ ] Add real-time features

## 🔍 Key Implementation Patterns

### Client Architecture
- Expo Router for file-based routing
- Redux Toolkit for state management
- JWT authentication with secure storage
- Component-based UI with theming

### Server Architecture
- Go with Gin framework
- PostgreSQL with GORM
- JWT middleware for authentication
- WebSocket hub for real-time features

## ⚠️ Critical Notes for AI

1. **Authentication is fundamental** - Start with auth system
2. **File naming matters** - Use kebab-case for components
3. **State management** - All API calls use Redux async thunks
4. **Navigation guards** - Authentication drives routing logic
5. **Type safety** - TypeScript throughout the client

## 📁 File Priority for Code Generation

**High Priority (Must implement first):**
- `app/_layout.tsx` - App initialization and auth routing
- `store/slices/authSlice.ts` - Authentication state
- `services/api/authAPI.ts` - API integration
- `components/themed-*.tsx` - Basic UI components

**Medium Priority:**
- Individual screen components
- API service files
- Server handlers and models

**Low Priority:**
- Advanced features
- Optimization code
- Non-critical utilities

This index ensures efficient project regeneration by providing clear priorities and avoiding information overload.