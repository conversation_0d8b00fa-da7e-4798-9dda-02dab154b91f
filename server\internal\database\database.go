package database

import (
	"fmt"
	"log"
	"os"
	"time"

	"gorm.io/driver/postgres"
	"gorm.io/driver/sqlite"
	"gorm.io/gorm"
	"gorm.io/gorm/logger"

	"quester-server/internal/models"
)

// Initialize initializes the database connection
func Initialize(databaseURL string) (*gorm.DB, error) {
	var db *gorm.DB
	var err error

	// Configure GORM logger
	logLevel := logger.Error
	if os.Getenv("ENVIRONMENT") == "development" {
		logLevel = logger.Info
	}

	config := &gorm.Config{
		Logger: logger.Default.LogMode(logLevel),
		NowFunc: func() time.Time {
			return time.Now().UTC()
		},
	}

	// Determine database type from URL
	if databaseURL == "" {
		return nil, fmt.Errorf("database URL is required")
	}

	// Check if it's a PostgreSQL URL
	if len(databaseURL) > 10 && databaseURL[:10] == "postgres://" {
		// PostgreSQL connection
		db, err = gorm.Open(postgres.Open(databaseURL), config)
		if err != nil {
			return nil, fmt.Errorf("failed to connect to PostgreSQL database: %w", err)
		}
		log.Println("Connected to PostgreSQL database")
	} else if len(databaseURL) > 9 && databaseURL[:9] == "sqlite://" {
		// SQLite connection (remove sqlite:// prefix)
		sqlitePath := databaseURL[9:]
		db, err = gorm.Open(sqlite.Open(sqlitePath), config)
		if err != nil {
			return nil, fmt.Errorf("failed to connect to SQLite database: %w", err)
		}
		log.Println("Connected to SQLite database")
	} else {
		// Assume it's a file path for SQLite
		db, err = gorm.Open(sqlite.Open(databaseURL), config)
		if err != nil {
			return nil, fmt.Errorf("failed to connect to SQLite database: %w", err)
		}
		log.Println("Connected to SQLite database")
	}

	// Configure connection pool for PostgreSQL
	if sqlDB, err := db.DB(); err == nil {
		// SetMaxIdleConns sets the maximum number of connections in the idle connection pool
		sqlDB.SetMaxIdleConns(10)

		// SetMaxOpenConns sets the maximum number of open connections to the database
		sqlDB.SetMaxOpenConns(100)

		// SetConnMaxLifetime sets the maximum amount of time a connection may be reused
		sqlDB.SetConnMaxLifetime(time.Hour)
	}

	return db, nil
}

// Migrate runs database migrations
func Migrate(db *gorm.DB) error {
	log.Println("Running database migrations...")

	// Auto-migrate all models
	err := db.AutoMigrate(
		&models.User{},
		&models.Quest{},
		&models.Task{},
		&models.Reward{},
		&models.UserReward{},
		&models.Category{},
		&models.Tag{},
		&models.Comment{},
		&models.Attachment{},
		&models.Notification{},
		&models.UserSession{},
		&models.ActivityLog{},
		&models.UserStats{},
		&models.QuestTemplate{},
		&models.TaskTemplate{},
		&models.Team{},
		&models.TeamMember{},
		&models.TeamQuest{},
		&models.UserPreference{},
		&models.APIKey{},
	)

	if err != nil {
		return fmt.Errorf("failed to run migrations: %w", err)
	}

	log.Println("Database migrations completed successfully")
	return nil
}

// CreateTestData creates sample data for development and testing
func CreateTestData(db *gorm.DB) error {
	if os.Getenv("ENVIRONMENT") != "development" {
		return nil // Only create test data in development
	}

	log.Println("Creating test data...")

	// Create test categories
	categories := []models.Category{
		{Name: "Personal Development", Description: "Self-improvement and learning goals", Color: "#4F46E5"},
		{Name: "Health & Fitness", Description: "Physical and mental wellness goals", Color: "#059669"},
		{Name: "Career", Description: "Professional development and work goals", Color: "#DC2626"},
		{Name: "Hobbies", Description: "Fun activities and personal interests", Color: "#7C3AED"},
		{Name: "Family & Relationships", Description: "Social connections and family time", Color: "#EA580C"},
	}

	for _, category := range categories {
		var existingCategory models.Category
		if err := db.Where("name = ?", category.Name).First(&existingCategory).Error; err != nil {
			if err == gorm.ErrRecordNotFound {
				if err := db.Create(&category).Error; err != nil {
					return fmt.Errorf("failed to create category %s: %w", category.Name, err)
				}
			} else {
				return fmt.Errorf("failed to check for existing category: %w", err)
			}
		}
	}

	// Create test tags
	tags := []models.Tag{
		{Name: "learning", Color: "#3B82F6"},
		{Name: "exercise", Color: "#10B981"},
		{Name: "reading", Color: "#8B5CF6"},
		{Name: "coding", Color: "#F59E0B"},
		{Name: "meditation", Color: "#06B6D4"},
		{Name: "creative", Color: "#EF4444"},
	}

	for _, tag := range tags {
		var existingTag models.Tag
		if err := db.Where("name = ?", tag.Name).First(&existingTag).Error; err != nil {
			if err == gorm.ErrRecordNotFound {
				if err := db.Create(&tag).Error; err != nil {
					return fmt.Errorf("failed to create tag %s: %w", tag.Name, err)
				}
			} else {
				return fmt.Errorf("failed to check for existing tag: %w", err)
			}
		}
	}

	log.Println("Test data created successfully")
	return nil
}

// Seed runs database seeding (for production initial data)
func Seed(db *gorm.DB) error {
	log.Println("Running database seeding...")

	// Create default categories if they don't exist
	defaultCategories := []models.Category{
		{Name: "General", Description: "General purpose category", Color: "#6B7280"},
		{Name: "Personal", Description: "Personal goals and tasks", Color: "#4F46E5"},
		{Name: "Work", Description: "Work-related goals and tasks", Color: "#059669"},
	}

	for _, category := range defaultCategories {
		var existingCategory models.Category
		if err := db.Where("name = ?", category.Name).First(&existingCategory).Error; err != nil {
			if err == gorm.ErrRecordNotFound {
				if err := db.Create(&category).Error; err != nil {
					return fmt.Errorf("failed to create default category %s: %w", category.Name, err)
				}
			} else {
				return fmt.Errorf("failed to check for existing category: %w", err)
			}
		}
	}

	log.Println("Database seeding completed successfully")
	return nil
}

// HealthCheck performs a basic health check on the database
func HealthCheck(db *gorm.DB) error {
	sqlDB, err := db.DB()
	if err != nil {
		return fmt.Errorf("failed to get database instance: %w", err)
	}

	if err := sqlDB.Ping(); err != nil {
		return fmt.Errorf("failed to ping database: %w", err)
	}

	return nil
}

// GetStats returns database statistics
func GetStats(db *gorm.DB) (map[string]interface{}, error) {
	sqlDB, err := db.DB()
	if err != nil {
		return nil, fmt.Errorf("failed to get database instance: %w", err)
	}

	stats := sqlDB.Stats()
	
	return map[string]interface{}{
		"max_open_connections":     stats.MaxOpenConnections,
		"open_connections":         stats.OpenConnections,
		"in_use":                  stats.InUse,
		"idle":                    stats.Idle,
		"wait_count":              stats.WaitCount,
		"wait_duration":           stats.WaitDuration,
		"max_idle_closed":         stats.MaxIdleClosed,
		"max_idle_time_closed":    stats.MaxIdleTimeClosed,
		"max_lifetime_closed":     stats.MaxLifetimeClosed,
	}, nil
}
