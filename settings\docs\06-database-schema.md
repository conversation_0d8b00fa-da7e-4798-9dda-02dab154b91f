# Database Schema & Models

## Database Configuration

### PostgreSQL Setup
```sql
-- Create database
CREATE DATABASE quester;

-- Create user (optional)
CREATE USER quester_user WITH PASSWORD 'your_password';
GRANT ALL PRIVILEGES ON DATABASE quester TO quester_user;
```

### Connection String Format
```
postgresql://username:password@localhost:5432/quester
```

## Core Table Schemas

### Users Table
```sql
CREATE TABLE users (
    id SERIAL PRIMARY KEY,
    email VARCHAR(255) UNIQUE NOT NULL,
    username VARCHAR(100) UNIQUE NOT NULL,
    first_name VARCHAR(100) NOT NULL,
    last_name VA<PERSON><PERSON><PERSON>(100) NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    avatar VARCHAR(255),
    level INTEGER DEFAULT 1,
    total_points INTEGER DEFAULT 0,
    is_verified BOOLEAN DEFAULT FALSE,
    is_active BOOLEAN DEFAULT TRUE,
    role VARCHAR(50) DEFAULT 'user',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    deleted_at TIMESTAMP
);

-- Indexes
CREATE INDEX idx_users_email ON users(email);
CREATE INDEX idx_users_username ON users(username);
CREATE INDEX idx_users_active ON users(is_active);
```

### Quests Table
```sql
CREATE TABLE quests (
    id SERIAL PRIMARY KEY,
    title VARCHAR(255) NOT NULL,
    description TEXT,
    difficulty VARCHAR(50) DEFAULT 'medium',
    priority VARCHAR(50) DEFAULT 'medium',
    status VARCHAR(50) DEFAULT 'pending',
    points INTEGER DEFAULT 0,
    created_by_id INTEGER REFERENCES users(id) ON DELETE SET NULL,
    assigned_to_id INTEGER REFERENCES users(id) ON DELETE SET NULL,
    due_date TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    deleted_at TIMESTAMP
);

-- Indexes
CREATE INDEX idx_quests_status ON quests(status);
CREATE INDEX idx_quests_created_by ON quests(created_by_id);
CREATE INDEX idx_quests_assigned_to ON quests(assigned_to_id);
CREATE INDEX idx_quests_due_date ON quests(due_date);
```

### Tasks Table
```sql
CREATE TABLE tasks (
    id SERIAL PRIMARY KEY,
    quest_id INTEGER REFERENCES quests(id) ON DELETE CASCADE,
    title VARCHAR(255) NOT NULL,
    description TEXT,
    status VARCHAR(50) DEFAULT 'pending',
    priority VARCHAR(50) DEFAULT 'medium',
    points INTEGER DEFAULT 0,
    assigned_to_id INTEGER REFERENCES users(id) ON DELETE SET NULL,
    due_date TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    deleted_at TIMESTAMP
);

-- Indexes
CREATE INDEX idx_tasks_quest_id ON tasks(quest_id);
CREATE INDEX idx_tasks_status ON tasks(status);
CREATE INDEX idx_tasks_assigned_to ON tasks(assigned_to_id);
```

### Messages Table
```sql
CREATE TABLE messages (
    id SERIAL PRIMARY KEY,
    sender_id INTEGER REFERENCES users(id) ON DELETE CASCADE,
    receiver_id INTEGER REFERENCES users(id) ON DELETE CASCADE,
    content TEXT NOT NULL,
    message_type VARCHAR(50) DEFAULT 'text',
    is_read BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    deleted_at TIMESTAMP
);

-- Indexes
CREATE INDEX idx_messages_sender ON messages(sender_id);
CREATE INDEX idx_messages_receiver ON messages(receiver_id);
CREATE INDEX idx_messages_conversation ON messages(sender_id, receiver_id);
CREATE INDEX idx_messages_unread ON messages(receiver_id, is_read);
```

### Notifications Table
```sql
CREATE TABLE notifications (
    id SERIAL PRIMARY KEY,
    user_id INTEGER REFERENCES users(id) ON DELETE CASCADE,
    type VARCHAR(50) NOT NULL,
    title VARCHAR(255) NOT NULL,
    message TEXT,
    is_read BOOLEAN DEFAULT FALSE,
    metadata JSONB,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    deleted_at TIMESTAMP
);

-- Indexes
CREATE INDEX idx_notifications_user ON notifications(user_id);
CREATE INDEX idx_notifications_unread ON notifications(user_id, is_read);
CREATE INDEX idx_notifications_type ON notifications(type);
```

## GORM Models (Go)

### Base Model
```go
package models

import (
    "time"
    "gorm.io/gorm"
)

type BaseModel struct {
    ID        uint           `json:"id" gorm:"primaryKey"`
    CreatedAt time.Time      `json:"created_at"`
    UpdatedAt time.Time      `json:"updated_at"`
    DeletedAt gorm.DeletedAt `json:"-" gorm:"index"`
}
```

### User Model
```go
type User struct {
    BaseModel
    Email        string `json:"email" gorm:"uniqueIndex;not null"`
    Username     string `json:"username" gorm:"uniqueIndex;not null"`
    FirstName    string `json:"first_name" gorm:"not null"`
    LastName     string `json:"last_name" gorm:"not null"`
    PasswordHash string `json:"-" gorm:"not null"`
    Avatar       string `json:"avatar"`
    Level        int    `json:"level" gorm:"default:1"`
    TotalPoints  int    `json:"total_points" gorm:"default:0"`
    IsVerified   bool   `json:"is_verified" gorm:"default:false"`
    IsActive     bool   `json:"is_active" gorm:"default:true"`
    Role         string `json:"role" gorm:"default:user"`

    // Relationships
    CreatedQuests []Quest `json:"created_quests,omitempty" gorm:"foreignKey:CreatedByID"`
    AssignedQuests []Quest `json:"assigned_quests,omitempty" gorm:"foreignKey:AssignedToID"`
    AssignedTasks []Task  `json:"assigned_tasks,omitempty" gorm:"foreignKey:AssignedToID"`
    SentMessages []Message `json:"sent_messages,omitempty" gorm:"foreignKey:SenderID"`
    ReceivedMessages []Message `json:"received_messages,omitempty" gorm:"foreignKey:ReceiverID"`
    Notifications []Notification `json:"notifications,omitempty" gorm:"foreignKey:UserID"`
}
```

### Quest Model
```go
type Quest struct {
    BaseModel
    Title       string     `json:"title" gorm:"not null"`
    Description string     `json:"description"`
    Difficulty  string     `json:"difficulty" gorm:"default:medium"`
    Priority    string     `json:"priority" gorm:"default:medium"`
    Status      string     `json:"status" gorm:"default:pending"`
    Points      int        `json:"points" gorm:"default:0"`
    DueDate     *time.Time `json:"due_date"`

    // Foreign Keys
    CreatedByID  uint  `json:"created_by_id"`
    AssignedToID *uint `json:"assigned_to_id"`

    // Relationships
    CreatedBy  User   `json:"created_by" gorm:"foreignKey:CreatedByID"`
    AssignedTo *User  `json:"assigned_to" gorm:"foreignKey:AssignedToID"`
    Tasks      []Task `json:"tasks" gorm:"foreignKey:QuestID"`
}
```

### Task Model
```go
type Task struct {
    BaseModel
    Title        string     `json:"title" gorm:"not null"`
    Description  string     `json:"description"`
    Status       string     `json:"status" gorm:"default:pending"`
    Priority     string     `json:"priority" gorm:"default:medium"`
    Points       int        `json:"points" gorm:"default:0"`
    DueDate      *time.Time `json:"due_date"`

    // Foreign Keys
    QuestID      uint  `json:"quest_id"`
    AssignedToID *uint `json:"assigned_to_id"`

    // Relationships
    Quest      Quest `json:"quest" gorm:"foreignKey:QuestID"`
    AssignedTo *User `json:"assigned_to" gorm:"foreignKey:AssignedToID"`
}
```

### Message Model
```go
type Message struct {
    BaseModel
    Content     string `json:"content" gorm:"not null"`
    MessageType string `json:"message_type" gorm:"default:text"`
    IsRead      bool   `json:"is_read" gorm:"default:false"`

    // Foreign Keys
    SenderID   uint `json:"sender_id"`
    ReceiverID uint `json:"receiver_id"`

    // Relationships
    Sender   User `json:"sender" gorm:"foreignKey:SenderID"`
    Receiver User `json:"receiver" gorm:"foreignKey:ReceiverID"`
}
```

### Notification Model
```go
type Notification struct {
    BaseModel
    Type     string          `json:"type" gorm:"not null"`
    Title    string          `json:"title" gorm:"not null"`
    Message  string          `json:"message"`
    IsRead   bool            `json:"is_read" gorm:"default:false"`
    Metadata datatypes.JSON  `json:"metadata" gorm:"type:jsonb"`

    // Foreign Keys
    UserID uint `json:"user_id"`

    // Relationships
    User User `json:"user" gorm:"foreignKey:UserID"`
}
```

## Database Migration

### Auto Migration (GORM)
```go
func RunMigrations(db *gorm.DB) error {
    return db.AutoMigrate(
        &User{},
        &Quest{},
        &Task{},
        &Message{},
        &Notification{},
    )
}
```

### Manual Migration Files
```sql
-- migrations/001_initial_schema.sql
-- Create initial tables with constraints

-- migrations/002_add_indexes.sql
-- Add performance indexes

-- migrations/003_add_triggers.sql
-- Add updated_at triggers
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_users_updated_at BEFORE UPDATE ON users
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
```

## Enum Definitions

### Status Values
```go
const (
    StatusPending    = "pending"
    StatusInProgress = "in_progress"
    StatusCompleted  = "completed"
    StatusCancelled  = "cancelled"
    StatusOnHold     = "on_hold"
)

const (
    PriorityLow      = "low"
    PriorityMedium   = "medium"
    PriorityHigh     = "high"
    PriorityUrgent   = "urgent"
)

const (
    DifficultyEasy   = "easy"
    DifficultyMedium = "medium"
    DifficultyHard   = "hard"
    DifficultyExpert = "expert"
)
```

## Database Relationships

### One-to-Many Relationships
- User → Quests (created_by)
- User → Tasks (assigned_to)
- User → Messages (sender/receiver)
- User → Notifications
- Quest → Tasks

### Many-to-Many Relationships (Future)
- Users ↔ Teams
- Teams ↔ Quests
- Users ↔ Skills

## Query Patterns

### Common Queries
```go
// Get user with quests
var user User
db.Preload("CreatedQuests").Preload("AssignedQuests").First(&user, userID)

// Get quest with tasks
var quest Quest
db.Preload("Tasks").Preload("CreatedBy").First(&quest, questID)

// Get unread messages
var messages []Message
db.Where("receiver_id = ? AND is_read = false", userID).Find(&messages)

// Get active quests for user
var quests []Quest
db.Where("assigned_to_id = ? AND status != ?", userID, "completed").Find(&quests)
```

## Database Performance

### Indexing Strategy
- Primary keys (automatic)
- Foreign keys for relationships
- Frequently queried columns (status, email, etc.)
- Composite indexes for common query patterns

### Connection Pooling
```go
sqlDB, _ := db.DB()
sqlDB.SetMaxIdleConns(10)
sqlDB.SetMaxOpenConns(100)
sqlDB.SetConnMaxLifetime(time.Hour)
```

## Backup & Maintenance

### Backup Command
```bash
pg_dump -h localhost -U username quester > backup.sql
```

### Restore Command
```bash
psql -h localhost -U username -d quester < backup.sql
```

This schema provides a solid foundation for the Quester application with proper relationships, indexes, and scalability considerations.