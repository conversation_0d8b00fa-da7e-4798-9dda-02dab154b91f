# Testing Guide

## Overview

Comprehensive testing strategy for the Quester application covering unit tests, integration tests, API testing, and end-to-end testing for both client and server components.

## Server Testing (Go)

### Test Structure

```
server/
├── internal/
│   ├── handlers/
│   │   ├── auth_test.go
│   │   ├── quest_test.go
│   │   └── user_test.go
│   ├── models/
│   │   ├── user_test.go
│   │   └── quest_test.go
│   ├── services/
│   │   ├── auth_service_test.go
│   │   └── quest_service_test.go
│   └── utils/
│       └── jwt_test.go
├── testdata/
│   ├── fixtures/
│   └── mocks/
└── integration/
    ├── api_test.go
    └── websocket_test.go
```

### Test Configuration (server/internal/testconfig/config.go)

```go
package testconfig

import (
    "fmt"
    "log"
    "os"
    "testing"

    "gorm.io/driver/sqlite"
    "gorm.io/gorm"
    "gorm.io/gorm/logger"
    "quester-server/internal/models"
)

func SetupTestDB(t *testing.T) *gorm.DB {
    // Use in-memory SQLite for tests
    db, err := gorm.Open(sqlite.Open(":memory:"), &gorm.Config{
        Logger: logger.Default.LogLevel(logger.Silent),
    })
    if err != nil {
        t.Fatalf("Failed to connect to test database: %v", err)
    }

    // Auto-migrate schemas
    err = db.AutoMigrate(
        &models.User{},
        &models.Quest{},
        &models.Task{},
        &models.Message{},
        &models.Notification{},
    )
    if err != nil {
        t.Fatalf("Failed to migrate test database: %v", err)
    }

    return db
}

func SetupTestUser(db *gorm.DB) *models.User {
    user := &models.User{
        Email:     "<EMAIL>",
        Username:  "testuser",
        FirstName: "Test",
        LastName:  "User",
        PasswordHash: "$2a$10$example.hash.here", // bcrypt hash of "password"
        IsActive:  true,
    }

    db.Create(user)
    return user
}

func CleanupTestDB(db *gorm.DB) {
    sqlDB, _ := db.DB()
    sqlDB.Close()
}

func SetupTestEnv() {
    os.Setenv("JWT_SECRET", "test-secret-key-for-jwt-tokens")
    os.Setenv("JWT_REFRESH_SECRET", "test-refresh-secret-key")
    os.Setenv("UPLOAD_PATH", "/tmp/test-uploads")
}
```

### Unit Tests

#### Model Tests (server/internal/models/user_test.go)

```go
package models

import (
    "testing"
    "time"
    "github.com/stretchr/testify/assert"
    "quester-server/internal/testconfig"
)

func TestUserValidation(t *testing.T) {
    tests := []struct {
        name    string
        user    User
        wantErr bool
    }{
        {
            name: "valid user",
            user: User{
                Email:     "<EMAIL>",
                Username:  "testuser",
                FirstName: "Test",
                LastName:  "User",
                PasswordHash: "hashedpassword",
            },
            wantErr: false,
        },
        {
            name: "invalid email",
            user: User{
                Email:     "invalid-email",
                Username:  "testuser",
                FirstName: "Test",
                LastName:  "User",
            },
            wantErr: true,
        },
        {
            name: "missing username",
            user: User{
                Email:     "<EMAIL>",
                FirstName: "Test",
                LastName:  "User",
            },
            wantErr: true,
        },
    }

    for _, tt := range tests {
        t.Run(tt.name, func(t *testing.T) {
            err := tt.user.Validate()
            if tt.wantErr {
                assert.Error(t, err)
            } else {
                assert.NoError(t, err)
            }
        })
    }
}

func TestUserMethods(t *testing.T) {
    user := User{
        FirstName: "John",
        LastName:  "Doe",
        Level:     3,
        TotalPoints: 1500,
    }

    t.Run("FullName", func(t *testing.T) {
        assert.Equal(t, "John Doe", user.FullName())
    })

    t.Run("CanLevelUp", func(t *testing.T) {
        // Assuming 1000 points per level
        user.TotalPoints = 3500 // Should be able to level up to 4
        assert.True(t, user.CanLevelUp())

        user.TotalPoints = 2999 // Should not be able to level up
        assert.False(t, user.CanLevelUp())
    })
}
```

#### Service Tests (server/internal/services/auth_service_test.go)

```go
package services

import (
    "testing"
    "github.com/stretchr/testify/assert"
    "github.com/stretchr/testify/require"
    "quester-server/internal/models"
    "quester-server/internal/testconfig"
)

func TestAuthService_Register(t *testing.T) {
    testconfig.SetupTestEnv()
    db := testconfig.SetupTestDB(t)
    defer testconfig.CleanupTestDB(db)

    authService := NewAuthService(db)

    t.Run("successful registration", func(t *testing.T) {
        req := models.RegisterRequest{
            Email:     "<EMAIL>",
            Username:  "newuser",
            FirstName: "New",
            LastName:  "User",
            Password:  "password123",
        }

        response, err := authService.Register(req)

        assert.NoError(t, err)
        assert.NotNil(t, response)
        assert.Equal(t, req.Email, response.User.Email)
        assert.NotEmpty(t, response.AccessToken)
        assert.NotEmpty(t, response.RefreshToken)
    })

    t.Run("duplicate email", func(t *testing.T) {
        // Create first user
        req1 := models.RegisterRequest{
            Email:     "<EMAIL>",
            Username:  "user1",
            FirstName: "User",
            LastName:  "One",
            Password:  "password123",
        }
        _, err := authService.Register(req1)
        require.NoError(t, err)

        // Try to create another user with same email
        req2 := models.RegisterRequest{
            Email:     "<EMAIL>",
            Username:  "user2",
            FirstName: "User",
            LastName:  "Two",
            Password:  "password123",
        }
        _, err = authService.Register(req2)
        assert.Error(t, err)
        assert.Contains(t, err.Error(), "email already exists")
    })
}

func TestAuthService_Login(t *testing.T) {
    testconfig.SetupTestEnv()
    db := testconfig.SetupTestDB(t)
    defer testconfig.CleanupTestDB(db)

    authService := NewAuthService(db)

    // Create test user
    user := testconfig.SetupTestUser(db)

    t.Run("successful login", func(t *testing.T) {
        response, err := authService.Login(user.Email, "password")

        assert.NoError(t, err)
        assert.NotNil(t, response)
        assert.Equal(t, user.Email, response.User.Email)
        assert.NotEmpty(t, response.AccessToken)
    })

    t.Run("invalid password", func(t *testing.T) {
        _, err := authService.Login(user.Email, "wrongpassword")

        assert.Error(t, err)
        assert.Contains(t, err.Error(), "invalid credentials")
    })

    t.Run("user not found", func(t *testing.T) {
        _, err := authService.Login("<EMAIL>", "password")

        assert.Error(t, err)
        assert.Contains(t, err.Error(), "user not found")
    })
}
```

#### Handler Tests (server/internal/handlers/auth_test.go)

```go
package handlers

import (
    "bytes"
    "encoding/json"
    "net/http"
    "net/http/httptest"
    "testing"

    "github.com/gin-gonic/gin"
    "github.com/stretchr/testify/assert"
    "quester-server/internal/models"
    "quester-server/internal/services"
    "quester-server/internal/testconfig"
)

func setupTestRouter(db *gorm.DB) *gin.Engine {
    gin.SetMode(gin.TestMode)
    router := gin.New()

    authHandler := NewAuthHandler(db)

    api := router.Group("/api/v1")
    {
        auth := api.Group("/auth")
        {
            auth.POST("/register", authHandler.Register)
            auth.POST("/login", authHandler.Login)
            auth.GET("/me", authHandler.GetMe)
        }
    }

    return router
}

func TestAuthHandler_Register(t *testing.T) {
    testconfig.SetupTestEnv()
    db := testconfig.SetupTestDB(t)
    defer testconfig.CleanupTestDB(db)

    router := setupTestRouter(db)

    t.Run("successful registration", func(t *testing.T) {
        reqBody := models.RegisterRequest{
            Email:     "<EMAIL>",
            Username:  "testuser",
            FirstName: "Test",
            LastName:  "User",
            Password:  "password123",
        }

        jsonBody, _ := json.Marshal(reqBody)
        req, _ := http.NewRequest("POST", "/api/v1/auth/register", bytes.NewBuffer(jsonBody))
        req.Header.Set("Content-Type", "application/json")

        w := httptest.NewRecorder()
        router.ServeHTTP(w, req)

        assert.Equal(t, http.StatusCreated, w.Code)

        var response map[string]interface{}
        err := json.Unmarshal(w.Body.Bytes(), &response)
        assert.NoError(t, err)
        assert.True(t, response["success"].(bool))
        assert.NotNil(t, response["data"])
    })

    t.Run("invalid request body", func(t *testing.T) {
        req, _ := http.NewRequest("POST", "/api/v1/auth/register", bytes.NewBuffer([]byte("invalid json")))
        req.Header.Set("Content-Type", "application/json")

        w := httptest.NewRecorder()
        router.ServeHTTP(w, req)

        assert.Equal(t, http.StatusBadRequest, w.Code)
    })
}

func TestAuthHandler_Login(t *testing.T) {
    testconfig.SetupTestEnv()
    db := testconfig.SetupTestDB(t)
    defer testconfig.CleanupTestDB(db)

    router := setupTestRouter(db)

    // Create test user
    user := testconfig.SetupTestUser(db)

    t.Run("successful login", func(t *testing.T) {
        reqBody := models.LoginRequest{
            Email:    user.Email,
            Password: "password",
        }

        jsonBody, _ := json.Marshal(reqBody)
        req, _ := http.NewRequest("POST", "/api/v1/auth/login", bytes.NewBuffer(jsonBody))
        req.Header.Set("Content-Type", "application/json")

        w := httptest.NewRecorder()
        router.ServeHTTP(w, req)

        assert.Equal(t, http.StatusOK, w.Code)

        var response map[string]interface{}
        err := json.Unmarshal(w.Body.Bytes(), &response)
        assert.NoError(t, err)
        assert.True(t, response["success"].(bool))
    })
}
```

### Integration Tests (server/integration/api_test.go)

```go
package integration

import (
    "bytes"
    "encoding/json"
    "net/http"
    "net/http/httptest"
    "testing"

    "github.com/stretchr/testify/assert"
    "github.com/stretchr/testify/suite"
    "quester-server/internal/models"
    "quester-server/internal/testconfig"
)

type APITestSuite struct {
    suite.Suite
    router *gin.Engine
    db     *gorm.DB
    user   *models.User
    token  string
}

func (suite *APITestSuite) SetupSuite() {
    testconfig.SetupTestEnv()
    suite.db = testconfig.SetupTestDB(suite.T())
    suite.router = setupTestRouter(suite.db)

    // Create test user and get token
    suite.user = testconfig.SetupTestUser(suite.db)
    suite.token = suite.getAuthToken()
}

func (suite *APITestSuite) TearDownSuite() {
    testconfig.CleanupTestDB(suite.db)
}

func (suite *APITestSuite) getAuthToken() string {
    reqBody := models.LoginRequest{
        Email:    suite.user.Email,
        Password: "password",
    }

    jsonBody, _ := json.Marshal(reqBody)
    req, _ := http.NewRequest("POST", "/api/v1/auth/login", bytes.NewBuffer(jsonBody))
    req.Header.Set("Content-Type", "application/json")

    w := httptest.NewRecorder()
    suite.router.ServeHTTP(w, req)

    var response map[string]interface{}
    json.Unmarshal(w.Body.Bytes(), &response)
    data := response["data"].(map[string]interface{})
    return data["access_token"].(string)
}

func (suite *APITestSuite) TestQuestWorkflow() {
    // Test creating a quest
    questData := map[string]interface{}{
        "title":       "Test Quest",
        "description": "A test quest for integration testing",
        "priority":    "medium",
        "points":      100,
    }

    jsonBody, _ := json.Marshal(questData)
    req, _ := http.NewRequest("POST", "/api/v1/quests", bytes.NewBuffer(jsonBody))
    req.Header.Set("Content-Type", "application/json")
    req.Header.Set("Authorization", "Bearer "+suite.token)

    w := httptest.NewRecorder()
    suite.router.ServeHTTP(w, req)

    assert.Equal(suite.T(), http.StatusCreated, w.Code)

    var createResponse map[string]interface{}
    err := json.Unmarshal(w.Body.Bytes(), &createResponse)
    assert.NoError(suite.T(), err)

    questID := createResponse["data"].(map[string]interface{})["id"].(float64)

    // Test getting the quest
    req, _ = http.NewRequest("GET", fmt.Sprintf("/api/v1/quests/%.0f", questID), nil)
    req.Header.Set("Authorization", "Bearer "+suite.token)

    w = httptest.NewRecorder()
    suite.router.ServeHTTP(w, req)

    assert.Equal(suite.T(), http.StatusOK, w.Code)

    // Test updating the quest
    updateData := map[string]interface{}{
        "status": "active",
    }

    jsonBody, _ = json.Marshal(updateData)
    req, _ = http.NewRequest("PUT", fmt.Sprintf("/api/v1/quests/%.0f", questID), bytes.NewBuffer(jsonBody))
    req.Header.Set("Content-Type", "application/json")
    req.Header.Set("Authorization", "Bearer "+suite.token)

    w = httptest.NewRecorder()
    suite.router.ServeHTTP(w, req)

    assert.Equal(suite.T(), http.StatusOK, w.Code)
}

func TestAPITestSuite(t *testing.T) {
    suite.Run(t, new(APITestSuite))
}
```

### WebSocket Tests (server/integration/websocket_test.go)

```go
package integration

import (
    "encoding/json"
    "net/http/httptest"
    "strings"
    "testing"
    "time"

    "github.com/gorilla/websocket"
    "github.com/stretchr/testify/assert"
    "quester-server/internal/testconfig"
)

func TestWebSocketConnection(t *testing.T) {
    testconfig.SetupTestEnv()
    db := testconfig.SetupTestDB(t)
    defer testconfig.CleanupTestDB(db)

    // Setup test server
    router := setupTestRouter(db)
    server := httptest.NewServer(router)
    defer server.Close()

    // Convert HTTP URL to WebSocket URL
    wsURL := "ws" + strings.TrimPrefix(server.URL, "http") + "/ws?user_id=1"

    // Connect to WebSocket
    dialer := websocket.DefaultDialer
    conn, _, err := dialer.Dial(wsURL, nil)
    assert.NoError(t, err)
    defer conn.Close()

    // Test ping/pong
    t.Run("ping pong", func(t *testing.T) {
        message := map[string]interface{}{
            "type": "ping",
            "data": "test",
        }

        err = conn.WriteJSON(message)
        assert.NoError(t, err)

        // Read response
        var response map[string]interface{}
        err = conn.ReadJSON(&response)
        assert.NoError(t, err)
        assert.Equal(t, "pong", response["type"])
    })

    // Test chat message
    t.Run("chat message", func(t *testing.T) {
        message := map[string]interface{}{
            "type": "chat_message",
            "data": map[string]interface{}{
                "content":     "Hello, WebSocket!",
                "receiver_id": 2,
            },
        }

        err = conn.WriteJSON(message)
        assert.NoError(t, err)

        // Should receive acknowledgment or broadcast
        conn.SetReadDeadline(time.Now().Add(5 * time.Second))
        var response map[string]interface{}
        err = conn.ReadJSON(&response)
        assert.NoError(t, err)
    })
}
```

## Client Testing (React Native/TypeScript)

### Test Configuration (client/jest.config.js)

```javascript
module.exports = {
  preset: 'jest-expo',
  setupFilesAfterEnv: ['<rootDir>/src/test/setup.ts'],
  moduleNameMapping: {
    '^@/(.*)$': '<rootDir>/src/$1',
  },
  transformIgnorePatterns: [
    'node_modules/(?!(jest-)?@react-native|react-native|@react-native-community|expo|@expo|@unimodules)',
  ],
  testMatch: [
    '**/__tests__/**/*.(ts|tsx|js)',
    '**/*.(test|spec).(ts|tsx|js)',
  ],
  collectCoverageFrom: [
    'src/**/*.{ts,tsx}',
    '!src/**/*.d.ts',
    '!src/test/**/*',
  ],
  coverageThreshold: {
    global: {
      branches: 70,
      functions: 70,
      lines: 70,
      statements: 70,
    },
  },
};
```

### Test Setup (client/src/test/setup.ts)

```typescript
import 'react-native-gesture-handler/jestSetup';
import mockAsyncStorage from '@react-native-async-storage/async-storage/jest/async-storage-mock';

// Mock AsyncStorage
jest.mock('@react-native-async-storage/async-storage', () => mockAsyncStorage);

// Mock Expo SecureStore
jest.mock('expo-secure-store', () => ({
  getItemAsync: jest.fn(),
  setItemAsync: jest.fn(),
  deleteItemAsync: jest.fn(),
}));

// Mock react-native-reanimated
jest.mock('react-native-reanimated', () => {
  const Reanimated = require('react-native-reanimated/mock');
  Reanimated.default.call = () => {};
  return Reanimated;
});

// Mock Expo Router
jest.mock('expo-router', () => ({
  useRouter: () => ({
    push: jest.fn(),
    replace: jest.fn(),
    back: jest.fn(),
  }),
  useSegments: () => [],
  Stack: {
    Screen: 'Screen',
  },
}));

// Mock WebSocket
global.WebSocket = jest.fn().mockImplementation(() => ({
  addEventListener: jest.fn(),
  removeEventListener: jest.fn(),
  send: jest.fn(),
  close: jest.fn(),
  readyState: WebSocket.OPEN,
}));

// Silence console warnings in tests
console.warn = jest.fn();
console.error = jest.fn();
```

### Test Utilities (client/src/test/utils.tsx)

```typescript
import React from 'react';
import { render, RenderOptions } from '@testing-library/react-native';
import { Provider } from 'react-redux';
import { configureStore } from '@reduxjs/toolkit';
import authSlice from '../store/slices/authSlice';
import messageSlice from '../store/slices/messageSlice';
import questSlice from '../store/slices/questSlice';

// Create a test store
function createTestStore(initialState = {}) {
  return configureStore({
    reducer: {
      auth: authSlice,
      messages: messageSlice,
      quests: questSlice,
    },
    preloadedState: initialState,
  });
}

// Custom render function with Redux provider
export function renderWithRedux(
  ui: React.ReactElement,
  {
    initialState = {},
    store = createTestStore(initialState),
    ...renderOptions
  }: {
    initialState?: any;
    store?: any;
  } & Omit<RenderOptions, 'wrapper'> = {}
) {
  function Wrapper({ children }: { children: React.ReactNode }) {
    return <Provider store={store}>{children}</Provider>;
  }

  return {
    ...render(ui, { wrapper: Wrapper, ...renderOptions }),
    store,
  };
}

// Mock user data
export const mockUser = {
  id: 1,
  email: '<EMAIL>',
  username: 'testuser',
  first_name: 'Test',
  last_name: 'User',
  avatar: null,
  level: 1,
  total_points: 0,
};

// Mock quest data
export const mockQuest = {
  id: 1,
  title: 'Test Quest',
  description: 'A test quest',
  status: 'active',
  priority: 'medium',
  progress: 50,
  points: 100,
  created_by: mockUser,
  tasks: [],
  participants: [],
};

// API mocking utilities
export function mockApiResponse(data: any, status = 200) {
  return Promise.resolve({
    data,
    status,
    statusText: 'OK',
    headers: {},
    config: {},
  });
}

export function mockApiError(message: string, status = 400) {
  return Promise.reject({
    response: {
      data: { message },
      status,
    },
  });
}
```

### Component Tests (client/src/components/ui/__tests__/Button.test.tsx)

```typescript
import React from 'react';
import { fireEvent } from '@testing-library/react-native';
import Button from '../Button';
import { renderWithRedux } from '../../../test/utils';

describe('Button Component', () => {
  it('renders correctly with text', () => {
    const { getByText } = renderWithRedux(
      <Button onPress={() => {}}>Test Button</Button>
    );

    expect(getByText('Test Button')).toBeTruthy();
  });

  it('calls onPress when pressed', () => {
    const onPressMock = jest.fn();
    const { getByText } = renderWithRedux(
      <Button onPress={onPressMock}>Press Me</Button>
    );

    fireEvent.press(getByText('Press Me'));
    expect(onPressMock).toHaveBeenCalledTimes(1);
  });

  it('is disabled when loading', () => {
    const onPressMock = jest.fn();
    const { getByText } = renderWithRedux(
      <Button onPress={onPressMock} loading={true}>
        Loading Button
      </Button>
    );

    const button = getByText('Loading Button').parent;
    expect(button.props.accessibilityState.disabled).toBe(true);

    fireEvent.press(getByText('Loading Button'));
    expect(onPressMock).not.toHaveBeenCalled();
  });

  it('applies correct variant styles', () => {
    const { getByText } = renderWithRedux(
      <Button variant="secondary" onPress={() => {}}>
        Secondary Button
      </Button>
    );

    // Check if secondary styles are applied
    const button = getByText('Secondary Button').parent;
    // Add specific style assertions based on your implementation
  });
});
```

### Redux Tests (client/src/store/slices/__tests__/authSlice.test.ts)

```typescript
import authSlice, {
  login,
  register,
  logout,
  selectUser,
  selectIsAuthenticated,
} from '../authSlice';
import { mockApiResponse, mockApiError, mockUser } from '../../../test/utils';

// Mock the auth API
jest.mock('../../../services/api/authAPI', () => ({
  authAPI: {
    login: jest.fn(),
    register: jest.fn(),
    getMe: jest.fn(),
  },
}));

describe('authSlice', () => {
  const initialState = {
    user: null,
    isAuthenticated: false,
    isInitialized: false,
    accessToken: null,
    refreshToken: null,
    isLoading: false,
    error: null,
  };

  it('should return the initial state', () => {
    expect(authSlice(undefined, { type: undefined })).toEqual(initialState);
  });

  describe('login async thunk', () => {
    it('should handle successful login', async () => {
      const credentials = { email: '<EMAIL>', password: 'password' };
      const responseData = {
        user: mockUser,
        access_token: 'access_token',
        refresh_token: 'refresh_token',
      };

      const dispatch = jest.fn();
      const getState = jest.fn();

      // Mock successful API response
      require('../../../services/api/authAPI').authAPI.login.mockResolvedValue(
        responseData
      );

      const action = login(credentials);
      await action(dispatch, getState, undefined);

      expect(dispatch).toHaveBeenCalledWith(
        expect.objectContaining({
          type: login.pending.type,
        })
      );

      expect(dispatch).toHaveBeenCalledWith(
        expect.objectContaining({
          type: login.fulfilled.type,
          payload: responseData,
        })
      );
    });

    it('should handle failed login', async () => {
      const credentials = { email: '<EMAIL>', password: 'wrong' };
      const errorMessage = 'Invalid credentials';

      const dispatch = jest.fn();
      const getState = jest.fn();

      // Mock failed API response
      require('../../../services/api/authAPI').authAPI.login.mockRejectedValue(
        new Error(errorMessage)
      );

      const action = login(credentials);
      await action(dispatch, getState, undefined);

      expect(dispatch).toHaveBeenCalledWith(
        expect.objectContaining({
          type: login.rejected.type,
          payload: errorMessage,
        })
      );
    });
  });

  describe('selectors', () => {
    const stateWithUser = {
      auth: {
        ...initialState,
        user: mockUser,
        isAuthenticated: true,
      },
    };

    it('should select user', () => {
      expect(selectUser(stateWithUser)).toEqual(mockUser);
    });

    it('should select authentication status', () => {
      expect(selectIsAuthenticated(stateWithUser)).toBe(true);
    });
  });
});
```

### Screen Tests (client/src/app/(auth)/__tests__/login.test.tsx)

```typescript
import React from 'react';
import { fireEvent, waitFor } from '@testing-library/react-native';
import LoginScreen from '../login';
import { renderWithRedux } from '../../../test/utils';

// Mock the useRouter hook
const mockPush = jest.fn();
jest.mock('expo-router', () => ({
  useRouter: () => ({
    push: mockPush,
    replace: jest.fn(),
  }),
}));

describe('LoginScreen', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders login form elements', () => {
    const { getByPlaceholderText, getByText } = renderWithRedux(<LoginScreen />);

    expect(getByPlaceholderText('Email')).toBeTruthy();
    expect(getByPlaceholderText('Password')).toBeTruthy();
    expect(getByText('Login')).toBeTruthy();
    expect(getByText('Register')).toBeTruthy();
  });

  it('shows validation errors for empty fields', async () => {
    const { getByText, getByTestId } = renderWithRedux(<LoginScreen />);

    const loginButton = getByText('Login');
    fireEvent.press(loginButton);

    await waitFor(() => {
      expect(getByText('Email is required')).toBeTruthy();
      expect(getByText('Password is required')).toBeTruthy();
    });
  });

  it('submits form with valid data', async () => {
    const { getByPlaceholderText, getByText } = renderWithRedux(<LoginScreen />);

    const emailInput = getByPlaceholderText('Email');
    const passwordInput = getByPlaceholderText('Password');
    const loginButton = getByText('Login');

    fireEvent.changeText(emailInput, '<EMAIL>');
    fireEvent.changeText(passwordInput, 'password123');
    fireEvent.press(loginButton);

    // Verify that login action is dispatched
    await waitFor(() => {
      // Add assertions based on your implementation
    });
  });

  it('navigates to register screen', () => {
    const { getByText } = renderWithRedux(<LoginScreen />);

    const registerButton = getByText('Register');
    fireEvent.press(registerButton);

    expect(mockPush).toHaveBeenCalledWith('/(auth)/register');
  });
});
```

### API Service Tests (client/src/services/api/__tests__/authAPI.test.ts)

```typescript
import { authAPI } from '../authAPI';
import { mockApiResponse, mockApiError } from '../../../test/utils';

// Mock axios
jest.mock('axios');

describe('authAPI', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('login', () => {
    it('should login successfully', async () => {
      const credentials = { email: '<EMAIL>', password: 'password' };
      const responseData = {
        user: { id: 1, email: '<EMAIL>' },
        access_token: 'token',
        refresh_token: 'refresh',
      };

      // Mock successful response
      require('axios').post.mockResolvedValue(mockApiResponse(responseData));

      const result = await authAPI.login(credentials);

      expect(result).toEqual(responseData);
      expect(require('axios').post).toHaveBeenCalledWith('/auth/login', credentials);
    });

    it('should handle login error', async () => {
      const credentials = { email: '<EMAIL>', password: 'wrong' };

      require('axios').post.mockRejectedValue(mockApiError('Invalid credentials'));

      await expect(authAPI.login(credentials)).rejects.toThrow('Invalid credentials');
    });
  });

  describe('register', () => {
    it('should register successfully', async () => {
      const userData = {
        email: '<EMAIL>',
        username: 'testuser',
        first_name: 'Test',
        last_name: 'User',
        password: 'password123',
      };

      const responseData = {
        user: { id: 1, ...userData },
        access_token: 'token',
        refresh_token: 'refresh',
      };

      require('axios').post.mockResolvedValue(mockApiResponse(responseData));

      const result = await authAPI.register(userData);

      expect(result).toEqual(responseData);
    });
  });
});
```

## E2E Testing with Detox

### Detox Configuration (.detoxrc.js)

```javascript
module.exports = {
  testRunner: 'jest',
  runnerConfig: 'e2e/config.json',
  skipLegacyWorkersInjection: true,
  apps: {
    'ios.debug': {
      type: 'ios.app',
      binaryPath: 'ios/build/Build/Products/Debug-iphonesimulator/Quester.app',
      build: 'xcodebuild -workspace ios/Quester.xcworkspace -scheme Quester -configuration Debug -sdk iphonesimulator -derivedDataPath ios/build',
    },
    'android.debug': {
      type: 'android.apk',
      binaryPath: 'android/app/build/outputs/apk/debug/app-debug.apk',
      build: 'cd android && ./gradlew assembleDebug assembleAndroidTest -DtestBuildType=debug',
    },
  },
  devices: {
    simulator: {
      type: 'ios.simulator',
      device: {
        type: 'iPhone 14',
      },
    },
    emulator: {
      type: 'android.emulator',
      device: {
        avdName: 'Pixel_API_30',
      },
    },
  },
  configurations: {
    'ios.sim.debug': {
      device: 'simulator',
      app: 'ios.debug',
    },
    'android.emu.debug': {
      device: 'emulator',
      app: 'android.debug',
    },
  },
};
```

### E2E Tests (e2e/auth.e2e.js)

```javascript
describe('Authentication Flow', () => {
  beforeAll(async () => {
    await device.launchApp();
  });

  beforeEach(async () => {
    await device.reloadReactNative();
  });

  it('should show login screen on app launch', async () => {
    await expect(element(by.text('Welcome to Quester'))).toBeVisible();
    await expect(element(by.id('email-input'))).toBeVisible();
    await expect(element(by.id('password-input'))).toBeVisible();
    await expect(element(by.id('login-button'))).toBeVisible();
  });

  it('should login successfully with valid credentials', async () => {
    await element(by.id('email-input')).typeText('<EMAIL>');
    await element(by.id('password-input')).typeText('password123');
    await element(by.id('login-button')).tap();

    await waitFor(element(by.text('Dashboard')))
      .toBeVisible()
      .withTimeout(5000);
  });

  it('should show error for invalid credentials', async () => {
    await element(by.id('email-input')).typeText('<EMAIL>');
    await element(by.id('password-input')).typeText('wrongpassword');
    await element(by.id('login-button')).tap();

    await expect(element(by.text('Invalid credentials'))).toBeVisible();
  });

  it('should navigate to register screen', async () => {
    await element(by.id('register-link')).tap();
    await expect(element(by.text('Create Account'))).toBeVisible();
  });
});

describe('Quest Management', () => {
  beforeAll(async () => {
    await device.launchApp();
    // Login first
    await element(by.id('email-input')).typeText('<EMAIL>');
    await element(by.id('password-input')).typeText('password123');
    await element(by.id('login-button')).tap();

    await waitFor(element(by.text('Dashboard')))
      .toBeVisible()
      .withTimeout(5000);
  });

  it('should create a new quest', async () => {
    await element(by.id('quests-tab')).tap();
    await element(by.id('create-quest-button')).tap();

    await expect(element(by.text('Create Quest'))).toBeVisible();

    await element(by.id('quest-title-input')).typeText('Test Quest');
    await element(by.id('quest-description-input')).typeText('A test quest for E2E testing');
    await element(by.id('save-quest-button')).tap();

    await waitFor(element(by.text('Test Quest')))
      .toBeVisible()
      .withTimeout(3000);
  });

  it('should view quest details', async () => {
    await element(by.text('Test Quest')).tap();
    await expect(element(by.text('Quest Details'))).toBeVisible();
    await expect(element(by.text('A test quest for E2E testing'))).toBeVisible();
  });
});
```

## Load Testing

### Artillery Configuration (load-test/config.yml)

```yaml
config:
  target: 'http://localhost:8080'
  phases:
    - duration: 60
      arrivalRate: 5
      name: 'Warm up'
    - duration: 120
      arrivalRate: 10
      name: 'Ramp up load'
    - duration: 300
      arrivalRate: 20
      name: 'Sustained load'
  payload:
    path: './users.csv'
    fields:
      - 'email'
      - 'password'

scenarios:
  - name: 'Authentication and Quest Creation'
    weight: 70
    flow:
      - post:
          url: '/api/v1/auth/login'
          json:
            email: '{{ email }}'
            password: '{{ password }}'
          capture:
            - json: '$.data.access_token'
              as: 'token'
      - post:
          url: '/api/v1/quests'
          headers:
            Authorization: 'Bearer {{ token }}'
          json:
            title: 'Load Test Quest {{ $randomString() }}'
            description: 'Quest created during load testing'
            priority: 'medium'
      - get:
          url: '/api/v1/quests'
          headers:
            Authorization: 'Bearer {{ token }}'

  - name: 'WebSocket Connection'
    weight: 30
    engine: ws
    flow:
      - connect:
          url: 'ws://localhost:8080/ws?user_id=1'
      - send:
          payload: '{"type":"ping","data":"test"}'
      - think: 5
      - send:
          payload: '{"type":"chat_message","data":{"content":"Load test message"}}'
```

## Test Scripts

### Package.json Scripts

```json
{
  "scripts": {
    "test": "jest",
    "test:watch": "jest --watch",
    "test:coverage": "jest --coverage",
    "test:ci": "jest --ci --coverage --watchAll=false",
    "test:e2e": "detox test",
    "test:e2e:build": "detox build",
    "test:load": "artillery run load-test/config.yml",
    "test:api": "newman run postman/Quester-API.postman_collection.json -e postman/test.postman_environment.json"
  }
}
```

### Server Test Scripts (server/Makefile)

```makefile
.PHONY: test test-unit test-integration test-coverage

test: test-unit test-integration

test-unit:
	go test -v ./internal/...

test-integration:
	go test -v ./integration/...

test-coverage:
	go test -coverprofile=coverage.out ./...
	go tool cover -html=coverage.out -o coverage.html

test-race:
	go test -race -v ./...

bench:
	go test -bench=. -benchmem ./...

test-clean:
	go clean -testcache
```

This comprehensive testing guide covers all aspects of testing the Quester application, from unit tests to end-to-end testing, ensuring code quality and reliability across both client and server components.