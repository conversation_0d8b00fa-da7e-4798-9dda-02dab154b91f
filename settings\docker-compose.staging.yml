
services:
  server:
    build:
      context: ../
      dockerfile: settings/server.dockerfile
      target: production
    ports:
      - "8000:8000"
    environment:
      - GO_ENV=staging
      - SERVER_PORT=8000
      - DATABASE_URL=${DATABASE_URL}
      - REDIS_URL=${REDIS_URL}
      - JWT_SECRET=${JWT_SECRET}
    depends_on:
      - postgres
      - redis
    networks:
      - quester-staging
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  client:
    build:
      context: ../
      dockerfile: settings/client.dockerfile
      target: production-web
    ports:
      - "3000:80"
    environment:
      - NODE_ENV=staging
      - REACT_APP_API_URL=${API_URL}
    depends_on:
      - server
    networks:
      - quester-staging
    restart: unless-stopped

  postgres:
    image: postgres:15-alpine
    environment:
      - POSTGRES_DB=${POSTGRES_DB}
      - POSTGRES_USER=${POSTGRES_USER}
      - POSTGRES_PASSWORD=${POSTGRES_PASSWORD}
    volumes:
      - postgres_staging_data:/var/lib/postgresql/data
      - ./backups:/backups
    networks:
      - quester-staging
    restart: unless-stopped

  redis:
    image: redis:7-alpine
    volumes:
      - redis_staging_data:/data
    networks:
      - quester-staging
    restart: unless-stopped
    command: redis-server --appendonly yes

  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/staging.conf:/etc/nginx/conf.d/default.conf
      - ./ssl:/etc/nginx/ssl
    depends_on:
      - server
      - client
    networks:
      - quester-staging
    restart: unless-stopped

volumes:
  postgres_staging_data:
  redis_staging_data:

networks:
  quester-staging:
    driver: bridge