{"permissions": {"allow": ["Bash(go mod:*)", "Bash(go build:*)", "<PERSON><PERSON>(chmod:*)", "Bash(npm install)", "Bash(./test-ui-integration.sh:*)", "Bash(npx tsc:*)", "Bash(./docker.sh dev:*)", "Bash(docker logs:*)", "Bash(docker-compose build:*)", "<PERSON><PERSON>(docker-compose:*)", "<PERSON><PERSON>(curl:*)", "Read(//c/Users/<USER>/.agent-os/instructions/**)", "Read(//c/Users/<USER>/.agent-os/**)", "Bash(tree:*)", "Bash(npm run lint)", "Bash(find:*)", "Bash(npm start)", "Bash(npx expo start:*)", "WebSearch", "Bash(npm init:*)", "Bash(npm install:*)", "<PERSON><PERSON>(mkdir:*)", "Bash(./.agent-os/verify-installation.sh:*)", "Bash(npm run:*)", "Bash(npm start:*)", "Bash(npx expo r:*)", "Bash(npm uninstall:*)", "Bash(npx expo install:*)", "<PERSON><PERSON>(go run:*)", "<PERSON><PERSON>(go test:*)", "<PERSON><PERSON>(docker restart:*)", "Bash(rm:*)", "<PERSON><PERSON>(docker stop quester-client-dev)", "<PERSON><PERSON>(docker start quester-client-dev)", "<PERSON><PERSON>(docker inspect:*)", "<PERSON><PERSON>(ipconfig)", "<PERSON><PERSON>(tasklist)", "<PERSON><PERSON>(timeout:*)", "Ba<PERSON>(go vet:*)", "Bash(staticcheck:*)", "Bash(ls:*)", "Bash(npm ls:*)", "Bash(docker build:*)", "<PERSON><PERSON>(docker run:*)", "<PERSON><PERSON>(docker rm:*)", "WebFetch(domain:reactnativereusables.com)", "Bash(go get:*)"], "deny": [], "ask": []}}