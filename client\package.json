{"name": "client", "main": "expo-router/entry", "version": "1.0.0", "scripts": {"dev": "expo start -c", "android": "expo start -c --android", "ios": "expo start -c --ios", "web": "expo start -c --web", "clean": "rm -rf .expo node_modules"}, "dependencies": {"@react-navigation/native": "^7.0.0", "@rn-primitives/portal": "~1.3.0", "@rn-primitives/slot": "^1.2.0", "class-variance-authority": "^0.7.0", "clsx": "^2.1.0", "expo": "^53.0.19", "expo-linking": "~7.1.7", "expo-router": "~5.1.3", "expo-splash-screen": "~0.30.10", "expo-status-bar": "~2.2.3", "expo-system-ui": "~5.0.10", "lucide-react-native": "^0.511.0", "nativewind": "4.1.23", "react": "19.0.0", "react-dom": "19.0.0", "react-native": "0.79.5", "react-native-reanimated": "~3.17.5", "react-native-safe-area-context": "5.4.0", "react-native-screens": "~4.11.1", "react-native-svg": "15.11.2", "react-native-web": "~0.20.0", "tailwind-merge": "^2.2.1", "tailwindcss": "^3.4.14", "tailwindcss-animate": "^1.0.7"}, "devDependencies": {"@babel/core": "^7.26.0", "@types/react": "~19.0.14", "prettier": "^3.6.2", "prettier-plugin-tailwindcss": "^0.6.14", "typescript": "~5.8.3"}, "resolutions": {"lightningcss": "1.27.0"}, "private": true}