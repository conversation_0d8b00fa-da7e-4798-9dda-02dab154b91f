package middleware

import (
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"

	"quester-server/internal/config"
	"quester-server/internal/models"
	"quester-server/internal/utils"
)

// AuthMiddleware handles authentication middleware
type AuthMiddleware struct {
	db         *gorm.DB
	cfg        *config.Config
	jwtManager *utils.JWTManager
}

// NewAuthMiddleware creates a new auth middleware instance
func NewAuthMiddleware(db *gorm.DB, cfg *config.Config) *AuthMiddleware {
	jwtManager := utils.NewJWTManager(
		cfg.JWTSecret,
		cfg.JWTExpirationHours,
		cfg.JWTRefreshDays,
	)
	return &AuthMiddleware{
		db:         db,
		cfg:        cfg,
		jwtManager: jwtManager,
	}
}

// RequireAuth middleware that requires authentication (alias for Authenticate)
func (m *AuthMiddleware) RequireAuth() gin.HandlerFunc {
	return m.Authenticate()
}

// Authenticate middleware to verify JWT tokens
func (m *AuthMiddleware) Authenticate() gin.HandlerFunc {
	return func(c *gin.Context) {
		// Get token from Authorization header
		authHeader := c.GetHeader("Authorization")
		token, err := utils.ExtractTokenFromHeader(authHeader)
		if err != nil {
			c.JSON(http.StatusUnauthorized, gin.H{
				"error":   "Unauthorized",
				"message": "Missing or invalid authorization header",
			})
			c.Abort()
			return
		}

		// Validate token
		claims, err := m.jwtManager.ValidateAccessToken(token)
		if err != nil {
			var statusCode int
			var message string

			switch err {
			case utils.ErrTokenExpired:
				statusCode = http.StatusUnauthorized
				message = "Token has expired"
			case utils.ErrTokenMalformed:
				statusCode = http.StatusBadRequest
				message = "Token is malformed"
			default:
				statusCode = http.StatusUnauthorized
				message = "Invalid token"
			}

			c.JSON(statusCode, gin.H{
				"error":   "Unauthorized",
				"message": message,
			})
			c.Abort()
			return
		}

		// Set user information in context
		c.Set("user_id", claims.UserID)
		c.Set("username", claims.Username)
		c.Set("email", claims.Email)
		c.Set("role", claims.Role)
		c.Set("claims", claims)

		c.Next()
	}
}

// RequireRole middleware to check if user has required role
func (m *AuthMiddleware) RequireRole(requiredRoles ...models.UserRole) gin.HandlerFunc {
	return func(c *gin.Context) {
		// Get user role from context
		role, exists := c.Get("role")
		if !exists {
			c.JSON(http.StatusUnauthorized, gin.H{
				"error":   "Unauthorized",
				"message": "Authentication required",
			})
			c.Abort()
			return
		}

		userRole := models.UserRole(role.(string))

		// Check if user has required role
		hasRole := false
		for _, requiredRole := range requiredRoles {
			if userRole == requiredRole {
				hasRole = true
				break
			}
		}

		if !hasRole {
			c.JSON(http.StatusForbidden, gin.H{
				"error":   "Forbidden",
				"message": "Insufficient permissions",
			})
			c.Abort()
			return
		}

		c.Next()
	}
}

// RequireAdmin middleware that requires admin privileges
func (m *AuthMiddleware) RequireAdmin() gin.HandlerFunc {
	return m.RequireRole(models.RoleAdmin)
}

// RequireModerator middleware to check if user is a moderator or admin
func (m *AuthMiddleware) RequireModerator() gin.HandlerFunc {
	return m.RequireRole(models.RoleAdmin, models.RoleModerator)
}

// OptionalAuth middleware that extracts user info if token is present but doesn't require it
func (m *AuthMiddleware) OptionalAuth() gin.HandlerFunc {
	return func(c *gin.Context) {
		// Get token from Authorization header
		authHeader := c.GetHeader("Authorization")
		token, err := utils.ExtractTokenFromHeader(authHeader)
		if err != nil {
			// No token provided, continue without authentication
			c.Next()
			return
		}

		// Validate token
		claims, err := m.jwtManager.ValidateAccessToken(token)
		if err != nil {
			// Invalid token, continue without authentication
			c.Next()
			return
		}

		// Set user information in context
		c.Set("user_id", claims.UserID)
		c.Set("username", claims.Username)
		c.Set("email", claims.Email)
		c.Set("role", claims.Role)
		c.Set("claims", claims)

		c.Next()
	}
}

// RequireOwnership middleware to check if user owns the resource
func (m *AuthMiddleware) RequireOwnership(resourceUserIDParam string) gin.HandlerFunc {
	return func(c *gin.Context) {
		// Get current user ID from context
		currentUserID, exists := c.Get("user_id")
		if !exists {
			c.JSON(http.StatusUnauthorized, gin.H{
				"error":   "Unauthorized",
				"message": "Authentication required",
			})
			c.Abort()
			return
		}

		// Get resource user ID from URL parameter
		resourceUserIDStr := c.Param(resourceUserIDParam)
		if resourceUserIDStr == "" {
			c.JSON(http.StatusBadRequest, gin.H{
				"error":   "Bad Request",
				"message": "Resource user ID required",
			})
			c.Abort()
			return
		}

		resourceUserID, err := strconv.ParseUint(resourceUserIDStr, 10, 32)
		if err != nil {
			c.JSON(http.StatusBadRequest, gin.H{
				"error":   "Bad Request",
				"message": "Invalid resource user ID",
			})
			c.Abort()
			return
		}

		// Check if user owns the resource or is an admin
		userRole, _ := c.Get("role")
		if currentUserID.(uint) != uint(resourceUserID) && userRole != string(models.RoleAdmin) {
			c.JSON(http.StatusForbidden, gin.H{
				"error":   "Forbidden",
				"message": "You don't have permission to access this resource",
			})
			c.Abort()
			return
		}

		c.Next()
	}
}

// GetCurrentUserID helper function to get current user ID from context
func GetCurrentUserID(c *gin.Context) (uint, bool) {
	userID, exists := c.Get("user_id")
	if !exists {
		return 0, false
	}
	return userID.(uint), true
}

// GetCurrentUserRole helper function to get current user role from context
func GetCurrentUserRole(c *gin.Context) (models.UserRole, bool) {
	role, exists := c.Get("role")
	if !exists {
		return "", false
	}
	return models.UserRole(role.(string)), true
}

// IsCurrentUserAdmin helper function to check if current user is admin
func IsCurrentUserAdmin(c *gin.Context) bool {
	role, exists := GetCurrentUserRole(c)
	return exists && role == models.RoleAdmin
}

// RateLimitMiddleware handles rate limiting
type RateLimitMiddleware struct {
	cfg *config.Config
}

// NewRateLimitMiddleware creates a new rate limit middleware instance
func NewRateLimitMiddleware(cfg *config.Config) *RateLimitMiddleware {
	return &RateLimitMiddleware{
		cfg: cfg,
	}
}

// RateLimit middleware that implements rate limiting
func (m *RateLimitMiddleware) RateLimit() gin.HandlerFunc {
	return func(c *gin.Context) {
		// Placeholder implementation - in a real app you would:
		// 1. Get client IP or user identifier
		// 2. Check rate limit (using Redis or in-memory store)
		// 3. Increment counter
		// 4. Return 429 if limit exceeded

		// For now, just continue
		c.Next()
	}
}

// LoggingMiddleware handles request logging
type LoggingMiddleware struct {
	cfg *config.Config
}

// NewLoggingMiddleware creates a new logging middleware instance
func NewLoggingMiddleware(cfg *config.Config) *LoggingMiddleware {
	return &LoggingMiddleware{
		cfg: cfg,
	}
}

// RequestLogger middleware that logs requests
func (m *LoggingMiddleware) RequestLogger() gin.HandlerFunc {
	return gin.LoggerWithConfig(gin.LoggerConfig{
		SkipPaths: []string{"/health"},
	})
}

// SecurityMiddleware handles security headers
type SecurityMiddleware struct {
	cfg *config.Config
}

// NewSecurityMiddleware creates a new security middleware instance
func NewSecurityMiddleware(cfg *config.Config) *SecurityMiddleware {
	return &SecurityMiddleware{
		cfg: cfg,
	}
}

// SecurityHeaders middleware that adds security headers
func (m *SecurityMiddleware) SecurityHeaders() gin.HandlerFunc {
	return func(c *gin.Context) {
		// Add security headers
		c.Header("X-Content-Type-Options", "nosniff")
		c.Header("X-Frame-Options", "DENY")
		c.Header("X-XSS-Protection", "1; mode=block")
		c.Header("Referrer-Policy", "strict-origin-when-cross-origin")

		if m.cfg.IsProduction() {
			c.Header("Strict-Transport-Security", "max-age=31536000; includeSubDomains")
		}

		c.Next()
	}
}