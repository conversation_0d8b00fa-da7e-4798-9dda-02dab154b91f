package config

import (
	"fmt"
	"os"
	"strconv"
)

// Config holds all configuration for the application
type Config struct {
	// Server configuration
	Port        string
	Environment string

	// Database configuration
	DatabaseURL string

	// Authentication configuration
	JWTSecret           string
	JWTExpirationHours  int
	JWTRefreshDays      int

	// Session configuration
	SessionSecret string

	// Redis configuration (for caching and sessions)
	RedisURL      string
	RedisPassword string
	RedisDB       int

	// Email configuration
	SMTPHost     string
	SMTPPort     int
	SMTPUsername string
	SMTPPassword string
	FromEmail    string

	// File upload configuration
	MaxUploadSize int64  // in bytes
	UploadPath    string

	// API configuration
	APIRateLimit      int  // requests per minute
	EnableRateLimit   bool
	EnableCompression bool

	// Security configuration
	EnableHTTPS    bool
	CertFile       string
	KeyFile        string
	TrustedProxies []string

	// Logging configuration
	LogLevel  string
	LogFormat string

	// External services
	OpenAIAPIKey string
	
	// WebSocket configuration
	MaxConnections        int
	ReadBufferSize        int
	WriteBufferSize       int
	HandshakeTimeout      int // in seconds
	PingPeriod            int // in seconds
	PongWait              int // in seconds
	WriteWait             int // in seconds
	MaxMessageSize        int64
}

// Load loads configuration from environment variables
func Load() (*Config, error) {
	cfg := &Config{
		// Server defaults
		Port:        getEnv("PORT", "8080"),
		Environment: getEnv("ENVIRONMENT", "development"),

		// Database
		DatabaseURL: getEnv("DATABASE_URL", "postgres://localhost/quester_dev?sslmode=disable"),

		// Authentication
		JWTSecret:          getEnv("JWT_SECRET", "your-secret-key-change-this-in-production"),
		JWTExpirationHours: getEnvAsInt("JWT_EXPIRATION_HOURS", 24),
		JWTRefreshDays:     getEnvAsInt("JWT_REFRESH_DAYS", 7),

		// Session
		SessionSecret: getEnv("SESSION_SECRET", "your-session-secret-change-this-in-production"),

		// Redis
		RedisURL:      getEnv("REDIS_URL", "redis://localhost:6379"),
		RedisPassword: getEnv("REDIS_PASSWORD", ""),
		RedisDB:       getEnvAsInt("REDIS_DB", 0),

		// Email
		SMTPHost:     getEnv("SMTP_HOST", "localhost"),
		SMTPPort:     getEnvAsInt("SMTP_PORT", 587),
		SMTPUsername: getEnv("SMTP_USERNAME", ""),
		SMTPPassword: getEnv("SMTP_PASSWORD", ""),
		FromEmail:    getEnv("FROM_EMAIL", "<EMAIL>"),

		// File upload
		MaxUploadSize: getEnvAsInt64("MAX_UPLOAD_SIZE", 10*1024*1024), // 10MB default
		UploadPath:    getEnv("UPLOAD_PATH", "./uploads"),

		// API
		APIRateLimit:      getEnvAsInt("API_RATE_LIMIT", 100),
		EnableRateLimit:   getEnvAsBool("ENABLE_RATE_LIMIT", true),
		EnableCompression: getEnvAsBool("ENABLE_COMPRESSION", true),

		// Security
		EnableHTTPS: getEnvAsBool("ENABLE_HTTPS", false),
		CertFile:    getEnv("CERT_FILE", ""),
		KeyFile:     getEnv("KEY_FILE", ""),

		// Logging
		LogLevel:  getEnv("LOG_LEVEL", "info"),
		LogFormat: getEnv("LOG_FORMAT", "json"),

		// External services
		OpenAIAPIKey: getEnv("OPENAI_API_KEY", ""),

		// WebSocket
		MaxConnections:   getEnvAsInt("WS_MAX_CONNECTIONS", 1000),
		ReadBufferSize:   getEnvAsInt("WS_READ_BUFFER_SIZE", 1024),
		WriteBufferSize:  getEnvAsInt("WS_WRITE_BUFFER_SIZE", 1024),
		HandshakeTimeout: getEnvAsInt("WS_HANDSHAKE_TIMEOUT", 10),
		PingPeriod:       getEnvAsInt("WS_PING_PERIOD", 54),
		PongWait:         getEnvAsInt("WS_PONG_WAIT", 60),
		WriteWait:        getEnvAsInt("WS_WRITE_WAIT", 10),
		MaxMessageSize:   getEnvAsInt64("WS_MAX_MESSAGE_SIZE", 512),
	}

	// Parse trusted proxies if provided
	if proxies := getEnv("TRUSTED_PROXIES", ""); proxies != "" {
		// Simple split by comma - you might want more sophisticated parsing
		cfg.TrustedProxies = []string{proxies}
	}

	// Validate required configuration
	if err := cfg.validate(); err != nil {
		return nil, fmt.Errorf("configuration validation failed: %w", err)
	}

	return cfg, nil
}

// validate checks that required configuration values are set
func (c *Config) validate() error {
	if c.DatabaseURL == "" {
		return fmt.Errorf("DATABASE_URL is required")
	}

	if c.JWTSecret == "your-secret-key-change-this-in-production" && c.Environment == "production" {
		return fmt.Errorf("JWT_SECRET must be changed in production")
	}

	if c.SessionSecret == "your-session-secret-change-this-in-production" && c.Environment == "production" {
		return fmt.Errorf("SESSION_SECRET must be changed in production")
	}

	if c.EnableHTTPS && (c.CertFile == "" || c.KeyFile == "") {
		return fmt.Errorf("CERT_FILE and KEY_FILE are required when HTTPS is enabled")
	}

	return nil
}

// getEnv gets an environment variable or returns a default value
func getEnv(key, defaultValue string) string {
	if value := os.Getenv(key); value != "" {
		return value
	}
	return defaultValue
}

// getEnvAsInt gets an environment variable as an integer or returns a default value
func getEnvAsInt(key string, defaultValue int) int {
	if value := os.Getenv(key); value != "" {
		if intValue, err := strconv.Atoi(value); err == nil {
			return intValue
		}
	}
	return defaultValue
}

// getEnvAsInt64 gets an environment variable as an int64 or returns a default value
func getEnvAsInt64(key string, defaultValue int64) int64 {
	if value := os.Getenv(key); value != "" {
		if intValue, err := strconv.ParseInt(value, 10, 64); err == nil {
			return intValue
		}
	}
	return defaultValue
}

// getEnvAsBool gets an environment variable as a boolean or returns a default value
func getEnvAsBool(key string, defaultValue bool) bool {
	if value := os.Getenv(key); value != "" {
		if boolValue, err := strconv.ParseBool(value); err == nil {
			return boolValue
		}
	}
	return defaultValue
}

// IsDevelopment returns true if the environment is development
func (c *Config) IsDevelopment() bool {
	return c.Environment == "development"
}

// IsProduction returns true if the environment is production
func (c *Config) IsProduction() bool {
	return c.Environment == "production"
}

// IsTest returns true if the environment is test
func (c *Config) IsTest() bool {
	return c.Environment == "test"
}
