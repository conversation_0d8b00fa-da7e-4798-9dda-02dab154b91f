# Go Fiber Backend Instructions

## Project Structure

```
server/
├── cmd/
│   └── server/
│       └── main.go        # Application entry point
├── internal/
│   ├── core/              # Core business logic
│   ├── database/          # Database configuration
│   ├── handlers/          # HTTP handlers
│   ├── models/            # Database models
│   ├── routes/            # Route definitions
│   └── testing/           # Test utilities
├── .air.toml             # Hot reload configuration
└── go.mod                # Go module file
```

## Database Models with GORM

### User Model
```go
package models

import (
    "time"
    "gorm.io/gorm"
)

type User struct {
    ID        uint           `gorm:"primarykey" json:"id"`
    CreatedAt time.Time      `json:"created_at"`
    UpdatedAt time.Time      `json:"updated_at"`
    DeletedAt gorm.DeletedAt `gorm:"index" json:"deleted_at"`

    Username string `gorm:"uniqueIndex;not null" json:"username"`
    Email    string `gorm:"uniqueIndex;not null" json:"email"`
    Password string `gorm:"not null" json:"-"`

    // Profile fields
    FirstName string `json:"first_name"`
    LastName  string `json:"last_name"`
    Avatar    string `json:"avatar"`

    // Relationships
    Quests        []Quest      `gorm:"foreignKey:UserID" json:"quests"`
    Tasks         []Task       `gorm:"foreignKey:AssignedTo" json:"tasks"`
    Messages      []Message    `gorm:"foreignKey:SenderID" json:"messages"`
    UserRoles     []UserRole   `gorm:"foreignKey:UserID" json:"user_roles"`
    Achievements  []Achievement `gorm:"many2many:user_achievements" json:"achievements"`
}

type Quest struct {
    ID          uint      `gorm:"primarykey" json:"id"`
    CreatedAt   time.Time `json:"created_at"`
    UpdatedAt   time.Time `json:"updated_at"`

    Title       string `gorm:"not null" json:"title"`
    Description string `json:"description"`
    Status      string `gorm:"default:'pending'" json:"status"`
    Priority    string `gorm:"default:'medium'" json:"priority"`

    UserID      uint `gorm:"not null" json:"user_id"`
    User        User `gorm:"foreignKey:UserID" json:"user"`

    Tasks       []Task `gorm:"foreignKey:QuestID" json:"tasks"`
}
```

## Fiber Route Handlers

### Authentication Handler
```go
package handlers

import (
    "github.com/gofiber/fiber/v2"
    "golang.org/x/crypto/bcrypt"
    "github.com/golang-jwt/jwt/v5"
    "time"
)

type AuthHandler struct {
    db *gorm.DB
}

func NewAuthHandler(db *gorm.DB) *AuthHandler {
    return &AuthHandler{db: db}
}

func (h *AuthHandler) Login(c *fiber.Ctx) error {
    var req LoginRequest
    if err := c.BodyParser(&req); err != nil {
        return c.Status(400).JSON(fiber.Map{
            "error": "Invalid request body",
        })
    }

    // Validate input
    if req.Email == "" || req.Password == "" {
        return c.Status(400).JSON(fiber.Map{
            "error": "Email and password are required",
        })
    }

    // Find user
    var user models.User
    if err := h.db.Where("email = ?", req.Email).First(&user).Error; err != nil {
        return c.Status(401).JSON(fiber.Map{
            "error": "Invalid credentials",
        })
    }

    // Verify password
    if err := bcrypt.CompareHashAndPassword([]byte(user.Password), []byte(req.Password)); err != nil {
        return c.Status(401).JSON(fiber.Map{
            "error": "Invalid credentials",
        })
    }

    // Generate JWT token
    token := jwt.NewWithClaims(jwt.SigningMethodHS256, jwt.MapClaims{
        "user_id": user.ID,
        "exp":     time.Now().Add(24 * time.Hour).Unix(),
    })

    tokenString, err := token.SignedString([]byte(os.Getenv("JWT_SECRET")))
    if err != nil {
        return c.Status(500).JSON(fiber.Map{
            "error": "Could not generate token",
        })
    }

    return c.JSON(fiber.Map{
        "token": tokenString,
        "user": fiber.Map{
            "id":         user.ID,
            "username":   user.Username,
            "email":      user.Email,
            "first_name": user.FirstName,
            "last_name":  user.LastName,
        },
    })
}

func (h *AuthHandler) Register(c *fiber.Ctx) error {
    var req RegisterRequest
    if err := c.BodyParser(&req); err != nil {
        return c.Status(400).JSON(fiber.Map{
            "error": "Invalid request body",
        })
    }

    // Hash password
    hashedPassword, err := bcrypt.GenerateFromPassword([]byte(req.Password), bcrypt.DefaultCost)
    if err != nil {
        return c.Status(500).JSON(fiber.Map{
            "error": "Could not hash password",
        })
    }

    // Create user
    user := models.User{
        Username:  req.Username,
        Email:     req.Email,
        Password:  string(hashedPassword),
        FirstName: req.FirstName,
        LastName:  req.LastName,
    }

    if err := h.db.Create(&user).Error; err != nil {
        return c.Status(400).JSON(fiber.Map{
            "error": "Could not create user",
        })
    }

    return c.Status(201).JSON(fiber.Map{
        "message": "User created successfully",
        "user": fiber.Map{
            "id":       user.ID,
            "username": user.Username,
            "email":    user.Email,
        },
    })
}
```

### CRUD Handler Pattern
```go
package handlers

type QuestHandler struct {
    db *gorm.DB
}

func NewQuestHandler(db *gorm.DB) *QuestHandler {
    return &QuestHandler{db: db}
}

func (h *QuestHandler) GetQuests(c *fiber.Ctx) error {
    var quests []models.Quest

    if err := h.db.Preload("User").Preload("Tasks").Find(&quests).Error; err != nil {
        return c.Status(500).JSON(fiber.Map{
            "error": "Could not fetch quests",
        })
    }

    return c.JSON(fiber.Map{
        "data": quests,
    })
}

func (h *QuestHandler) GetQuest(c *fiber.Ctx) error {
    id := c.Params("id")

    var quest models.Quest
    if err := h.db.Preload("User").Preload("Tasks").First(&quest, id).Error; err != nil {
        if errors.Is(err, gorm.ErrRecordNotFound) {
            return c.Status(404).JSON(fiber.Map{
                "error": "Quest not found",
            })
        }
        return c.Status(500).JSON(fiber.Map{
            "error": "Could not fetch quest",
        })
    }

    return c.JSON(fiber.Map{
        "data": quest,
    })
}

func (h *QuestHandler) CreateQuest(c *fiber.Ctx) error {
    var req CreateQuestRequest
    if err := c.BodyParser(&req); err != nil {
        return c.Status(400).JSON(fiber.Map{
            "error": "Invalid request body",
        })
    }

    userID := c.Locals("user_id").(uint)

    quest := models.Quest{
        Title:       req.Title,
        Description: req.Description,
        Status:      "pending",
        Priority:    req.Priority,
        UserID:      userID,
    }

    if err := h.db.Create(&quest).Error; err != nil {
        return c.Status(500).JSON(fiber.Map{
            "error": "Could not create quest",
        })
    }

    return c.Status(201).JSON(fiber.Map{
        "data": quest,
    })
}
```

## Middleware Patterns

### JWT Authentication Middleware
```go
package middleware

import (
    "strings"
    "github.com/gofiber/fiber/v2"
    "github.com/golang-jwt/jwt/v5"
)

func JWTAuth(c *fiber.Ctx) error {
    authHeader := c.Get("Authorization")
    if authHeader == "" {
        return c.Status(401).JSON(fiber.Map{
            "error": "Authorization header required",
        })
    }

    tokenString := strings.TrimPrefix(authHeader, "Bearer ")
    if tokenString == authHeader {
        return c.Status(401).JSON(fiber.Map{
            "error": "Bearer token required",
        })
    }

    token, err := jwt.Parse(tokenString, func(token *jwt.Token) (interface{}, error) {
        return []byte(os.Getenv("JWT_SECRET")), nil
    })

    if err != nil || !token.Valid {
        return c.Status(401).JSON(fiber.Map{
            "error": "Invalid token",
        })
    }

    claims := token.Claims.(jwt.MapClaims)
    userID := uint(claims["user_id"].(float64))

    c.Locals("user_id", userID)
    return c.Next()
}
```

### CORS Middleware
```go
package middleware

import "github.com/gofiber/fiber/v2/middleware/cors"

func CORS() fiber.Handler {
    return cors.New(cors.Config{
        AllowOrigins:     "http://localhost:19000,http://localhost:8081",
        AllowMethods:     "GET,POST,PUT,DELETE,OPTIONS",
        AllowHeaders:     "Origin,Content-Type,Accept,Authorization",
        AllowCredentials: true,
    })
}
```

## Route Registration

### Routes Setup
```go
package routes

import (
    "github.com/gofiber/fiber/v2"
    "github.com/gofiber/websocket/v2"
)

func SetupRoutes(app *fiber.App, handlers *Handlers) {
    // API v1 routes
    api := app.Group("/api/v1")

    // Auth routes (public)
    auth := api.Group("/auth")
    auth.Post("/login", handlers.Auth.Login)
    auth.Post("/register", handlers.Auth.Register)
    auth.Post("/logout", handlers.Auth.Logout)

    // Protected routes
    protected := api.Group("", middleware.JWTAuth)

    // User routes
    users := protected.Group("/users")
    users.Get("/", handlers.User.GetUsers)
    users.Get("/:id", handlers.User.GetUser)
    users.Put("/:id", handlers.User.UpdateUser)

    // Quest routes
    quests := protected.Group("/quests")
    quests.Get("/", handlers.Quest.GetQuests)
    quests.Post("/", handlers.Quest.CreateQuest)
    quests.Get("/:id", handlers.Quest.GetQuest)
    quests.Put("/:id", handlers.Quest.UpdateQuest)
    quests.Delete("/:id", handlers.Quest.DeleteQuest)

    // WebSocket routes
    app.Get("/ws/:id", websocket.New(handlers.WebSocket.HandleConnection))
}
```

## WebSocket Implementation

### WebSocket Handler
```go
package handlers

import (
    "log"
    "github.com/gofiber/websocket/v2"
)

type WebSocketHandler struct {
    clients map[string]*websocket.Conn
    mutex   sync.RWMutex
}

func NewWebSocketHandler() *WebSocketHandler {
    return &WebSocketHandler{
        clients: make(map[string]*websocket.Conn),
    }
}

func (h *WebSocketHandler) HandleConnection(c *websocket.Conn) {
    userID := c.Params("id")

    h.mutex.Lock()
    h.clients[userID] = c
    h.mutex.Unlock()

    defer func() {
        h.mutex.Lock()
        delete(h.clients, userID)
        h.mutex.Unlock()
        c.Close()
    }()

    for {
        var msg MessagePayload
        if err := c.ReadJSON(&msg); err != nil {
            log.Printf("WebSocket read error: %v", err)
            break
        }

        // Process message
        h.BroadcastMessage(msg)
    }
}

func (h *WebSocketHandler) BroadcastMessage(msg MessagePayload) {
    h.mutex.RLock()
    defer h.mutex.RUnlock()

    for userID, conn := range h.clients {
        if err := conn.WriteJSON(msg); err != nil {
            log.Printf("WebSocket write error for user %s: %v", userID, err)
            conn.Close()
            delete(h.clients, userID)
        }
    }
}
```

## Database Configuration

### Database Setup
```go
package database

import (
    "gorm.io/gorm"
    "gorm.io/driver/postgres"
)

func Connect() (*gorm.DB, error) {
    dsn := fmt.Sprintf(
        "host=%s user=%s password=%s dbname=%s port=%s sslmode=disable",
        os.Getenv("DB_HOST"),
        os.Getenv("DB_USER"),
        os.Getenv("DB_PASSWORD"),
        os.Getenv("DB_NAME"),
        os.Getenv("DB_PORT"),
    )

    db, err := gorm.Open(postgres.Open(dsn), &gorm.Config{})
    if err != nil {
        return nil, err
    }

    // Auto-migrate models
    if err := db.AutoMigrate(
        &models.User{},
        &models.Quest{},
        &models.Task{},
        &models.Message{},
        &models.Role{},
        &models.Permission{},
    ); err != nil {
        return nil, err
    }

    return db, nil
}
```

## Testing Patterns

### Handler Testing
```go
package handlers_test

import (
    "testing"
    "net/http/httptest"
    "github.com/gofiber/fiber/v2"
    "github.com/stretchr/testify/assert"
)

func TestLogin(t *testing.T) {
    app := fiber.New()

    // Setup test database and handler
    handler := NewAuthHandler(testDB)
    app.Post("/auth/login", handler.Login)

    req := httptest.NewRequest("POST", "/auth/login", strings.NewReader(`{
        "email": "<EMAIL>",
        "password": "password"
    }`))
    req.Header.Set("Content-Type", "application/json")

    resp, err := app.Test(req)
    assert.NoError(t, err)
    assert.Equal(t, 200, resp.StatusCode)
}
```