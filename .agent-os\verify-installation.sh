#!/bin/bash

# Quester Agent OS Installation Verification Script
# This script verifies that Agent OS has been properly installed and configured

echo "=== Quester Agent OS Installation Verification ==="
echo

# Colors for output
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Function to print status
print_status() {
    if [ $1 -eq 0 ]; then
        echo -e "${GREEN}✓${NC} $2"
    else
        echo -e "${RED}✗${NC} $2"
    fi
}

# Function to print warning
print_warning() {
    echo -e "${YELLOW}⚠${NC} $1"
}

# Check if we're in the correct directory
if [ ! -f "README.md" ] || [ ! -d "client" ] || [ ! -d "server" ]; then
    echo -e "${RED}Error: Please run this script from the Quester project root directory${NC}"
    exit 1
fi

echo "Checking Agent OS installation..."
echo

# 1. Check if .agent-os directory exists
if [ -d ".agent-os" ]; then
    print_status 0 ".agent-os directory exists"
else
    print_status 1 ".agent-os directory missing"
    exit 1
fi

# 2. Check required subdirectories
directories=("config" "instructions" "templates" "tools" "workflows")
for dir in "${directories[@]}"; do
    if [ -d ".agent-os/$dir" ]; then
        print_status 0 ".agent-os/$dir directory exists"
    else
        print_status 1 ".agent-os/$dir directory missing"
    fi
done

echo

# 3. Check configuration files
echo "Checking configuration files..."

config_files=(
    "config/agent.json"
    "instructions/main.md"
    "instructions/frontend-react-native.md"
    "instructions/backend-go-fiber.md"
    "instructions/docker-integration.md"
)

for file in "${config_files[@]}"; do
    if [ -f ".agent-os/$file" ]; then
        print_status 0 "$file exists"
    else
        print_status 1 "$file missing"
    fi
done

echo

# 4. Check template files
echo "Checking template files..."

template_files=(
    "templates/react-native-component.tsx"
    "templates/go-handler.go"
    "templates/go-model.go"
)

for file in "${template_files[@]}"; do
    if [ -f ".agent-os/$file" ]; then
        print_status 0 "$file exists"
    else
        print_status 1 "$file missing"
    fi
done

echo

# 5. Check workflow files
echo "Checking workflow files..."

workflow_files=(
    "workflows/development.json"
    "workflows/deployment.json"
)

for file in "${workflow_files[@]}"; do
    if [ -f ".agent-os/$file" ]; then
        print_status 0 "$file exists"
    else
        print_status 1 "$file missing"
    fi
done

echo

# 6. Check tool configurations
echo "Checking tool configurations..."

tool_files=(
    "tools/code-generator.json"
)

for file in "${tool_files[@]}"; do
    if [ -f ".agent-os/$file" ]; then
        print_status 0 "$file exists"
    else
        print_status 1 "$file missing"
    fi
done

echo

# 7. Check package.json for Agent OS
echo "Checking package.json..."
if [ -f "package.json" ]; then
    if grep -q "@autonomys/agent-os" package.json; then
        print_status 0 "Agent OS package found in package.json"
    else
        print_warning "Agent OS package not found in package.json (may be installed globally)"
    fi
else
    print_status 1 "package.json not found in project root"
fi

echo

# 8. Validate JSON configuration files
echo "Validating JSON configuration files..."

json_files=(
    ".agent-os/config/agent.json"
    ".agent-os/workflows/development.json"
    ".agent-os/workflows/deployment.json"
    ".agent-os/tools/code-generator.json"
)

for file in "${json_files[@]}"; do
    if [ -f "$file" ]; then
        if python -m json.tool "$file" > /dev/null 2>&1 || node -p "JSON.parse(require('fs').readFileSync('$file', 'utf8'))" > /dev/null 2>&1; then
            print_status 0 "$file is valid JSON"
        else
            print_status 1 "$file has invalid JSON syntax"
        fi
    fi
done

echo

# 9. Check project structure compatibility
echo "Checking project structure compatibility..."

required_dirs=("client" "server" "settings" "shared")
for dir in "${required_dirs[@]}"; do
    if [ -d "$dir" ]; then
        print_status 0 "$dir directory exists (required for Agent OS)"
    else
        print_status 1 "$dir directory missing"
    fi
done

echo

# 10. Check if Docker is available (for containerized workflows)
echo "Checking Docker availability..."
if command -v docker > /dev/null 2>&1; then
    print_status 0 "Docker is installed"
    if docker info > /dev/null 2>&1; then
        print_status 0 "Docker daemon is running"
    else
        print_warning "Docker daemon is not running"
    fi
else
    print_warning "Docker is not installed (required for containerized workflows)"
fi

if command -v docker-compose > /dev/null 2>&1; then
    print_status 0 "Docker Compose is installed"
else
    print_warning "Docker Compose is not installed"
fi

echo

# 11. Summary
echo "=== Installation Summary ==="
echo

if [ -d ".agent-os" ] && [ -f ".agent-os/config/agent.json" ]; then
    echo -e "${GREEN}✓ Agent OS has been successfully installed and configured for Quester!${NC}"
    echo
    echo "Available features:"
    echo "• React Native Expo frontend instructions and templates"
    echo "• Go Fiber backend patterns and handlers"
    echo "• Docker integration and deployment workflows"
    echo "• Code generation templates and tools"
    echo "• Development and production environment configurations"
    echo
    echo "Next steps:"
    echo "1. Review the instructions in .agent-os/instructions/"
    echo "2. Test the development workflow: .agent-os/workflows/development.json"
    echo "3. Generate code using the templates in .agent-os/templates/"
    echo "4. Use the Docker integration guide: .agent-os/instructions/docker-integration.md"
else
    echo -e "${RED}✗ Agent OS installation is incomplete${NC}"
    echo "Please re-run the installation process or check for missing files."
fi

echo