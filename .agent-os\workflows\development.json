{"name": "development-workflow", "description": "Development workflow for Quester full-stack application", "version": "1.0.0", "steps": [{"id": "environment-setup", "name": "Environment Setup", "description": "Initialize development environment", "commands": ["./env-switch.sh dev", "./docker.sh dev up -d", "sleep 10"], "validation": {"command": "./docker.sh dev status", "expected_output": "running"}}, {"id": "database-migration", "name": "Database Migration", "description": "Run database migrations", "depends_on": ["environment-setup"], "commands": ["docker-compose -f settings/docker-compose.dev.yml exec server go run cmd/migrate/main.go"]}, {"id": "install-dependencies", "name": "Install Dependencies", "description": "Install project dependencies", "parallel": true, "tasks": [{"name": "Install Go dependencies", "commands": ["cd server", "go mod download", "go mod tidy"]}, {"name": "Install Node dependencies", "commands": ["cd client", "npm install"]}]}, {"id": "code-quality-checks", "name": "Code Quality Checks", "description": "Run linting and formatting", "depends_on": ["install-dependencies"], "parallel": true, "tasks": [{"name": "Go formatting and linting", "commands": ["cd server", "go fmt ./...", "go vet ./...", "golangci-lint run"]}, {"name": "TypeScript linting", "commands": ["cd client", "npm run lint"]}]}, {"id": "run-tests", "name": "Run Tests", "description": "Execute test suites", "depends_on": ["code-quality-checks"], "parallel": true, "tasks": [{"name": "Go unit tests", "commands": ["cd server", "go test ./... -v"]}, {"name": "React Native tests", "commands": ["cd client", "npm test"]}]}, {"id": "start-development", "name": "Start Development Servers", "description": "Start development servers with hot reload", "depends_on": ["database-migration"], "background": true, "tasks": [{"name": "Start Go server with Air", "commands": ["docker-compose -f settings/docker-compose.dev.yml up server"]}, {"name": "Start Expo development server", "commands": ["docker-compose -f settings/docker-compose.dev.yml up client"]}]}], "environment_variables": {"NODE_ENV": "development", "GO_ENV": "development", "HOT_RELOAD": "true"}, "health_checks": [{"name": "PostgreSQL Health", "endpoint": "postgresql://localhost:5432/quester", "timeout": 30}, {"name": "Redis Health", "endpoint": "redis://localhost:6379", "timeout": 10}, {"name": "Server Health", "endpoint": "http://localhost:8000/health", "timeout": 30}, {"name": "Client Health", "endpoint": "http://localhost:19000", "timeout": 30}], "cleanup": {"commands": ["./docker.sh dev down", "docker system prune -f"]}}