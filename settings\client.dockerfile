# Multi-stage build for React Native Expo client application

# Base stage for dependencies
FROM node:22-alpine AS base
WORKDIR /app

# Install system dependencies for React Native
RUN apk add --no-cache \
    git \
    python3 \
    make \
    g++

# Expo CLI install
RUN npm install -g @expo/cli @expo/ngrok

# Copy package files
COPY client/package*.json ./
RUN npm install --legacy-peer-deps && npm cache clean --force
RUN npx expo install expo-dev-client

# Explicitly install @radix-ui dependencies to resolve nested dependency issues
RUN npm install @radix-ui/react-label @radix-ui/react-primitive @radix-ui/react-popover @radix-ui/react-separator

# Development stage
FROM base AS development
ENV NODE_ENV=development
ENV EXPO_DEVTOOLS_LISTEN_ADDRESS=0.0.0.0
ENV EXPO_NO_TELEMETRY=1
ENV REACT_NATIVE_PACKAGER_HOSTNAME=0.0.0.0
ENV WATCHMAN_DISABLE_CI=1
ENV METRO_ENABLE_FAST_REFRESH=1
WORKDIR /app

COPY client/ .
EXPOSE 8081
CMD ["npm", "run", "dev"]

# Build stage for web
FROM base AS build-web
ENV NODE_ENV=production
WORKDIR /app

COPY client/ .
# Ensure dependencies are available for web build
RUN npm install --legacy-peer-deps
RUN npx expo export -p web

# Build stage for standalone
FROM base AS build-standalone
ENV NODE_ENV=production
WORKDIR /app

COPY client/ .
# Ensure dependencies are available for standalone build
RUN npm install --legacy-peer-deps
# Build APK/IPA would require more setup
RUN npx expo build:web

# Production web stage (for Expo web builds)
FROM nginx:alpine AS production-web
WORKDIR /usr/share/nginx/html

# Remove default nginx website
RUN rm -rf ./*

# Copy built web app
COPY --from=build-web /app/dist .

# Copy nginx configuration
COPY settings/nginx/client.conf /etc/nginx/conf.d/default.conf

# Create non-root user
RUN addgroup -g 1001 -S appgroup && \
    adduser -S appuser -u 1001 -G appgroup

# Change ownership
RUN chown -R appuser:appgroup /usr/share/nginx/html
RUN chown -R appuser:appgroup /var/cache/nginx
RUN chown -R appuser:appgroup /var/log/nginx
RUN chown -R appuser:appgroup /etc/nginx/conf.d
RUN touch /var/run/nginx.pid
RUN chown -R appuser:appgroup /var/run/nginx.pid

USER appuser

EXPOSE 80
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
    CMD wget --no-verbose --tries=1 --spider http://localhost/ || exit 1

CMD ["nginx", "-g", "daemon off;"]

# Development server stage (for Expo development)
FROM base AS production
ENV NODE_ENV=production
ENV EXPO_DEVTOOLS_LISTEN_ADDRESS=0.0.0.0
WORKDIR /app

# Create non-root user
RUN addgroup -g 1001 -S appgroup && \
    adduser -S appuser -u 1001 -G appgroup

COPY --chown=appuser:appgroup client/ .

USER appuser
EXPOSE 8081

CMD ["npx", "expo", "start", "--no-dev", "--minify"]