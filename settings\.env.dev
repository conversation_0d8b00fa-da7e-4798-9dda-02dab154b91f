# Development Environment Variables
NODE_ENV=development
GO_ENV=development

# Database Configuration
DB_HOST=postgres
DB_PORT=5432
DB_USER=postgres
DB_PASSWORD=password
DB_NAME=quester_dev

# Redis Configuration
REDIS_HOST=redis
REDIS_PORT=6379

# API Configuration
API_URL=http://localhost:8000
SERVER_PORT=8000

# CORS Configuration
CORS_ALLOW_ORIGINS=http://localhost:8081,http://localhost:19000,http://localhost:3000

# JWT Configuration
JWT_SECRET=dev-super-secret-jwt-key-for-development-only-please-change-in-production-2024
JWT_ACCESS_TOKEN_EXPIRE=24h
JWT_REFRESH_TOKEN_EXPIRE=168h

# Email Configuration (MailHog)
SMTP_HOST=mailhog
SMTP_PORT=1025
SMTP_USER=
SMTP_PASSWORD=
FROM_EMAIL=<EMAIL>

# Expo Configuration
EXPO_DEVTOOLS_LISTEN_ADDRESS=0.0.0.0
REACT_NATIVE_PACKAGER_HOSTNAME=localhost
EXPO_PUBLIC_CLERK_PUBLISHABLE_KEY=pk_test_a25vd2luZy1jcmFuZS00OC5jbGVyay5hY2NvdW50cy5kZXYk
# Database Seeding Configuration
ENABLE_SEEDING=true
ENABLE_TEST_SEEDING=true

